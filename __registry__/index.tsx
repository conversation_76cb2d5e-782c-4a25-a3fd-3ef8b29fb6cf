// @ts-nocheck
// This file is autogenerated by scripts/build-registry.ts
// Do not edit this file directly.
import * as React from "react"

export const Index: Record<string, any> = {
  "default": {
    "hero": {
      name: "hero",
      type: "registry:block",
      registryDependencies: undefined,
      files: ["src/registry/section/hero.tsx"],
      component: React.lazy(() => import("@/src/registry/registry/section/hero.tsx")),
      source: "src/registry/registry/section/hero.tsx",
      category: "sections",
      subcategory: "hero",
      chunks: []
    },
    "hero-test": {
      name: "hero-test",
      type: "registry:block",
      registryDependencies: undefined,
      files: ["src/registry/section/hero-test.tsx"],
      component: React.lazy(() => import("@/src/registry/registry/section/hero-test.tsx")),
      source: "src/registry/registry/section/hero-test.tsx",
      category: "sections",
      subcategory: "hero",
      chunks: []
    },
    "pricing": {
      name: "pricing",
      type: "registry:block",
      registryDependencies: undefined,
      files: ["src/registry/section/pricing.tsx"],
      component: React.lazy(() => import("@/src/registry/section/pricing.tsx")),
      source: "src/registry/section/pricing.tsx",
      category: "sections",
      subcategory: "marketing",
      chunks: []
    },
    "word-reveal": {
      name: "word-reveal",
      type: "registry:ui",
      registryDependencies: undefined,
      files: ["src/components/prismui/word-reveal.tsx"],
      component: React.lazy(() => import("@/components/prismui/word-reveal.tsx")),
      source: "",
      category: "components",
      subcategory: "animation",
      chunks: []
    },
    "card": {
      name: "card",
      type: "registry:ui",
      registryDependencies: undefined,
      files: ["src/components/prismui/card.tsx"],
      component: React.lazy(() => import("@/components/prismui/card.tsx")),
      source: "",
      category: "components",
      subcategory: "layout",
      chunks: []
    },
    "logo-carousel": {
      name: "logo-carousel",
      type: "registry:ui",
      registryDependencies: undefined,
      files: ["src/components/prismui/logo-carousel.tsx"],
      component: React.lazy(() => import("@/components/prismui/logo-carousel.tsx")),
      source: "",
      category: "components",
      subcategory: "display",
      chunks: []
    },
    "floating-action-panel": {
      name: "floating-action-panel",
      type: "registry:ui",
      registryDependencies: undefined,
      files: ["src/components/prismui/floating-action-panel.tsx"],
      component: React.lazy(() => import("@/components/prismui/floating-action-panel.tsx")),
      source: "",
      category: "components",
      subcategory: "overlay",
      chunks: []
    },
    "hero-badge": {
      name: "hero-badge",
      type: "registry:ui",
      registryDependencies: undefined,
      files: ["src/components/prismui/hero-badge.tsx"],
      component: React.lazy(() => import("@/components/prismui/hero-badge.tsx")),
      source: "",
      category: "components",
      subcategory: "display",
      chunks: []
    },
    "action-button": {
      name: "action-button",
      type: "registry:ui",
      registryDependencies: undefined,
      files: ["src/components/prismui/action-button.tsx"],
      component: React.lazy(() => import("@/components/prismui/action-button.tsx")),
      source: "",
      category: "components",
      subcategory: "form",
      chunks: []
    },
    "button-group": {
      name: "button-group",
      type: "registry:ui",
      registryDependencies: undefined,
      files: ["src/components/prismui/button-group.tsx"],
      component: React.lazy(() => import("@/components/prismui/button-group.tsx")),
      source: "",
      category: "components",
      subcategory: "form",
      chunks: []
    },
    "expandable-card": {
      name: "expandable-card",
      type: "registry:ui",
      registryDependencies: undefined,
      files: ["src/components/prismui/expandable-card.tsx"],
      component: React.lazy(() => import("@/components/prismui/expandable-card.tsx")),
      source: "",
      category: "components",
      subcategory: "display",
      chunks: []
    },
    "display-cards": {
      name: "display-cards",
      type: "registry:ui",
      registryDependencies: undefined,
      files: ["src/components/prismui/display-cards.tsx"],
      component: React.lazy(() => import("@/components/prismui/display-cards.tsx")),
      source: "",
      category: "components",
      subcategory: "display",
      chunks: []
    },
    "hero": {
      name: "hero",
      type: "registry:ui",
      registryDependencies: undefined,
      files: ["src/components/prismui/hero.tsx"],
      component: React.lazy(() => import("@/components/prismui/hero.tsx")),
      source: "",
      category: "components",
      subcategory: "marketing",
      chunks: []
    },
    "open-source": {
      name: "open-source",
      type: "registry:ui",
      registryDependencies: undefined,
      files: ["src/components/prismui/open-source.tsx"],
      component: React.lazy(() => import("@/components/prismui/open-source.tsx")),
      source: "",
      category: "components",
      subcategory: "marketing",
      chunks: []
    },
    "number-flow": {
      name: "number-flow",
      type: "registry:ui",
      registryDependencies: undefined,
      files: ["src/components/prismui/number-flow.tsx"],
      component: React.lazy(() => import("@/components/prismui/number-flow.tsx")),
      source: "",
      category: "components",
      subcategory: "animation",
      chunks: []
    },
    "popover": {
      name: "popover",
      type: "registry:ui",
      registryDependencies: undefined,
      files: ["src/components/prismui/popover.tsx"],
      component: React.lazy(() => import("@/components/prismui/popover.tsx")),
      source: "",
      category: "components",
      subcategory: "overlay",
      chunks: []
    },
    "pricing": {
      name: "pricing",
      type: "registry:ui",
      registryDependencies: undefined,
      files: ["src/components/prismui/pricing.tsx"],
      component: React.lazy(() => import("@/components/prismui/pricing.tsx")),
      source: "",
      category: "sections",
      subcategory: "marketing",
      chunks: []
    },
    "timeline": {
      name: "timeline",
      type: "registry:ui",
      registryDependencies: undefined,
      files: ["src/components/prismui/timeline.tsx"],
      component: React.lazy(() => import("@/components/prismui/timeline.tsx")),
      source: "",
      category: "components",
      subcategory: "display",
      chunks: []
    },
  },
}
