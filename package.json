{"name": "prism-ui", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "build:registry": "tsx src/lib/scripts/build-registry.mts", "shadcn": "shadcn build"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@number-flow/react": "^0.4.4", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "@supabase/supabase-js": "^2.47.14", "@types/canvas-confetti": "^1.9.0", "@types/github-slugger": "^2.0.0", "@types/ms": "^0.7.34", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "embla-carousel-react": "^8.5.1", "framer-motion": "^11.3.21", "github-slugger": "^2.0.0", "input-otp": "^1.4.1", "lucide-react": "^0.417.0", "ms": "^2.1.3", "nanoid": "^5.0.9", "next": "14.2.7", "next-themes": "^0.3.0", "react": "^18.3.1", "react-day-picker": "8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-icons": "^5.2.1", "react-medium-image-zoom": "^5.2.12", "react-resizable-panels": "^2.1.7", "react-syntax-highlighter": "^15.6.1", "react-tweet": "^3.2.1", "recharts": "^2.15.0", "rehype-autolink-headings": "^7.1.0", "rehype-pretty-code": "^0.13.2", "rehype-slug": "^6.0.0", "rehype-stringify": "^10.0.0", "remark-gfm": "^4.0.0", "remark-parse": "^11.0.0", "remark-rehype": "^11.1.0", "shadcn": "2.2.0-canary.3", "shiki": "^1.24.4", "sonner": "^1.7.1", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "unified": "^11.0.5", "vaul": "^0.9.9", "vercel": "^39.2.5", "zod": "^3.24.1"}, "devDependencies": {"@content-collections/core": "^0.8.0", "@content-collections/mdx": "^0.2.0", "@content-collections/next": "^0.2.4", "@tailwindcss/typography": "^0.5.13", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.7", "lodash.template": "^4.5.0", "postcss": "^8", "rimraf": "^6.0.1", "tailwindcss": "^3.4.1", "ts-morph": "^24.0.0", "tsx": "^4.19.2", "typescript": "^5"}}