{"name": "floating-action-panel", "type": "registry:ui", "code": "\"use client\";\n\nimport * as React from \"react\";\nimport { AnimatePresence, MotionConfig, motion } from \"framer-motion\";\nimport { cn } from \"@/lib/utils\";\n\nconst TRANSITION = {\n  type: \"spring\",\n  bounce: 0.1,\n  duration: 0.4,\n};\n\ninterface FloatingActionPanelContextType {\n  isOpen: boolean;\n  openPanel: (rect: DOMRect, mode: \"actions\" | \"note\") => void;\n  closePanel: () => void;\n  uniqueId: string;\n  triggerRect: DOMRect | null;\n  title: string;\n  setTitle: (title: string) => void;\n  note: string;\n  setNote: (note: string) => void;\n  mode: \"actions\" | \"note\";\n}\n\nconst FloatingActionPanelContext = React.createContext<\n  FloatingActionPanelContextType | undefined\n>(undefined);\n\nfunction useFloatingActionPanelLogic() {\n  const uniqueId = React.useId();\n  const [isOpen, setIsOpen] = React.useState(false);\n  const [triggerRect, setTriggerRect] = React.useState<DOMRect | null>(null);\n  const [title, setTitle] = React.useState(\"\");\n  const [note, setNote] = React.useState(\"\");\n  const [mode, setMode] = React.useState<\"actions\" | \"note\">(\"actions\");\n\n  const openPanel = (rect: DOMRect, newMode: \"actions\" | \"note\") => {\n    setTriggerRect(rect);\n    setMode(newMode);\n    setIsOpen(true);\n  };\n  const closePanel = () => {\n    setIsOpen(false);\n    setNote(\"\");\n  };\n\n  return {\n    isOpen,\n    openPanel,\n    closePanel,\n    uniqueId,\n    triggerRect,\n    title,\n    setTitle,\n    note,\n    setNote,\n    mode,\n  };\n}\n\ninterface FloatingActionPanelRootProps {\n  children: (context: FloatingActionPanelContextType) => React.ReactNode;\n  className?: string;\n}\n\nexport function FloatingActionPanelRoot({\n  children,\n  className,\n}: FloatingActionPanelRootProps) {\n  const floatingPanelLogic = useFloatingActionPanelLogic();\n\n  return (\n    <FloatingActionPanelContext.Provider value={floatingPanelLogic}>\n      <MotionConfig transition={TRANSITION}>\n        <div className={cn(\"relative\", className)}>\n          {children(floatingPanelLogic)}\n        </div>\n      </MotionConfig>\n    </FloatingActionPanelContext.Provider>\n  );\n}\n\ninterface FloatingActionPanelTriggerProps {\n  children: React.ReactNode;\n  className?: string;\n  title: string;\n  mode: \"actions\" | \"note\";\n}\n\nexport function FloatingActionPanelTrigger({\n  children,\n  className,\n  title,\n  mode,\n}: FloatingActionPanelTriggerProps) {\n  const { openPanel, uniqueId, setTitle } = React.useContext(FloatingActionPanelContext)!;\n  const triggerRef = React.useRef<HTMLButtonElement>(null);\n\n  const handleClick = () => {\n    if (triggerRef.current) {\n      openPanel(triggerRef.current.getBoundingClientRect(), mode);\n      setTitle(title);\n    }\n  };\n\n  return (\n    <motion.button\n      ref={triggerRef}\n      layoutId={`floating-panel-trigger-${uniqueId}-${mode}`}\n      className={cn(\n        \"flex h-9 items-center rounded-md border border-zinc-200 bg-white px-3 text-sm font-medium text-zinc-900 shadow-sm hover:bg-zinc-50 dark:border-zinc-800 dark:bg-zinc-950 dark:text-zinc-50 dark:hover:bg-zinc-800\",\n        className\n      )}\n      onClick={handleClick}\n      whileHover={{ scale: 1.02 }}\n      whileTap={{ scale: 0.98 }}\n    >\n      {children}\n    </motion.button>\n  );\n}\n\ninterface FloatingActionPanelContentProps {\n  children?: React.ReactNode;\n  className?: string;\n}\n\nexport function FloatingActionPanelContent({\n  children,\n  className,\n}: FloatingActionPanelContentProps) {\n  const { isOpen, closePanel, uniqueId, triggerRect, title, mode } =\n    React.useContext(FloatingActionPanelContext)!;\n  const contentRef = React.useRef<HTMLDivElement>(null);\n\n  React.useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (\n        contentRef.current &&\n        !contentRef.current.contains(event.target as Node)\n      ) {\n        closePanel();\n      }\n    };\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => document.removeEventListener(\"mousedown\", handleClickOutside);\n  }, [closePanel]);\n\n  React.useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.key === \"Escape\") closePanel();\n    };\n    document.addEventListener(\"keydown\", handleKeyDown);\n    return () => document.removeEventListener(\"keydown\", handleKeyDown);\n  }, [closePanel]);\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <>\n          <motion.div\n            initial={{ backdropFilter: \"blur(0px)\" }}\n            animate={{ backdropFilter: \"blur(4px)\" }}\n            exit={{ backdropFilter: \"blur(0px)\" }}\n            className=\"fixed inset-0 z-40 bg-black/5\"\n          />\n          <motion.div\n            ref={contentRef}\n            layoutId={`floating-panel-${uniqueId}-${mode}`}\n            className={cn(\n              \"fixed z-50 min-w-[200px] overflow-hidden rounded-lg border border-zinc-200 bg-white shadow-lg outline-none dark:border-zinc-800 dark:bg-zinc-950\",\n              className\n            )}\n            style={{\n              left: triggerRect ? triggerRect.left : \"50%\",\n              top: triggerRect ? triggerRect.bottom + 8 : \"50%\",\n              transformOrigin: \"top left\",\n            }}\n            initial={{ opacity: 0, scale: 0.9, y: -8 }}\n            animate={{ opacity: 1, scale: 1, y: 0 }}\n            exit={{ opacity: 0, scale: 0.9, y: -8 }}\n          >\n            <div className=\"px-4 py-3 font-medium\">{title}</div>\n            {children}\n          </motion.div>\n        </>\n      )}\n    </AnimatePresence>\n  );\n}\n\ninterface FloatingActionPanelButtonProps {\n  children: React.ReactNode;\n  onClick?: () => void;\n  className?: string;\n}\n\nexport function FloatingActionPanelButton({\n  children,\n  onClick,\n  className,\n}: FloatingActionPanelButtonProps) {\n  return (\n    <motion.button\n      className={cn(\n        \"flex w-full items-center gap-2 rounded-md px-2 py-1.5 text-sm text-zinc-900 hover:bg-zinc-100 dark:text-zinc-50 dark:hover:bg-zinc-800\",\n        className\n      )}\n      onClick={onClick}\n      whileHover={{ backgroundColor: \"rgba(0, 0, 0, 0.05)\" }}\n      whileTap={{ scale: 0.98 }}\n    >\n      {children}\n    </motion.button>\n  );\n}\n\ninterface FloatingActionPanelFormProps {\n  children: React.ReactNode;\n  onSubmit?: (note: string) => void;\n  className?: string;\n}\n\nexport function FloatingActionPanelForm({\n  children,\n  onSubmit,\n  className,\n}: FloatingActionPanelFormProps) {\n  const { note, closePanel } = React.useContext(FloatingActionPanelContext)!;\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    onSubmit?.(note);\n    closePanel();\n  };\n\n  return (\n    <form\n      className={cn(\"flex h-full flex-col\", className)}\n      onSubmit={handleSubmit}\n    >\n      {children}\n    </form>\n  );\n}\n\ninterface FloatingActionPanelTextareaProps {\n  className?: string;\n  id?: string;\n}\n\nexport function FloatingActionPanelTextarea({\n  className,\n  id,\n}: FloatingActionPanelTextareaProps) {\n  const { note, setNote } = React.useContext(FloatingActionPanelContext)!;\n\n  return (\n    <textarea\n      id={id}\n      className={cn(\n        \"h-full w-full resize-none rounded-md bg-transparent px-4 py-3 text-sm outline-none\",\n        className\n      )}\n      autoFocus\n      value={note}\n      onChange={(e) => setNote(e.target.value)}\n    />\n  );\n}", "files": [{"path": "components/prismui/floating-action-panel.tsx", "type": "registry:ui", "content": "\"use client\";\n\nimport * as React from \"react\";\nimport { AnimatePresence, MotionConfig, motion } from \"framer-motion\";\nimport {\n  ArrowLeft,\n  Plus,\n  Upload,\n  Palette,\n  Share2,\n  BookMarked,\n  StickyNote,\n} from \"lucide-react\";\nimport { cn } from \"@/lib/utils\";\n\nconst TRANSITION = {\n  type: \"spring\",\n  bounce: 0.1,\n  duration: 0.4,\n};\n\ninterface FloatingActionPanelContextType {\n  isOpen: boolean;\n  openPanel: (rect: DOMRect, mode: \"actions\" | \"note\") => void;\n  closePanel: () => void;\n  uniqueId: string;\n  triggerRect: DOMRect | null;\n  title: string;\n  setTitle: (title: string) => void;\n  note: string;\n  setNote: (note: string) => void;\n  mode: \"actions\" | \"note\";\n}\n\nconst FloatingActionPanelContext = React.createContext<\n  FloatingActionPanelContextType | undefined\n>(undefined);\n\nconst useFloatingActionPanel = () => {\n  const context = React.useContext(FloatingActionPanelContext);\n  if (!context) {\n    throw new Error(\n      \"useFloatingActionPanel must be used within a FloatingActionPanelProvider\"\n    );\n  }\n  return context;\n};\n\nfunction useFloatingActionPanelLogic() {\n  const uniqueId = React.useId();\n  const [isOpen, setIsOpen] = React.useState(false);\n  const [triggerRect, setTriggerRect] = React.useState<DOMRect | null>(null);\n  const [title, setTitle] = React.useState(\"\");\n  const [note, setNote] = React.useState(\"\");\n  const [mode, setMode] = React.useState<\"actions\" | \"note\">(\"actions\");\n\n  const openPanel = (rect: DOMRect, newMode: \"actions\" | \"note\") => {\n    setTriggerRect(rect);\n    setMode(newMode);\n    setIsOpen(true);\n  };\n  const closePanel = () => {\n    setIsOpen(false);\n    setNote(\"\");\n  };\n\n  return {\n    isOpen,\n    openPanel,\n    closePanel,\n    uniqueId,\n    triggerRect,\n    title,\n    setTitle,\n    note,\n    setNote,\n    mode,\n  };\n}\n\ninterface FloatingActionPanelRootProps {\n  children: (context: FloatingActionPanelContextType) => React.ReactNode;\n  className?: string;\n}\n\nconst FloatingActionPanelRoot = React.forwardRef<\n  HTMLDivElement,\n  FloatingActionPanelRootProps\n>(({ children, className }, ref) => {\n  const floatingPanelLogic = useFloatingActionPanelLogic();\n\n  return (\n    <FloatingActionPanelContext.Provider value={floatingPanelLogic}>\n      <MotionConfig transition={TRANSITION}>\n        <div ref={ref} className={cn(\"relative\", className)}>\n          {children(floatingPanelLogic)}\n        </div>\n      </MotionConfig>\n    </FloatingActionPanelContext.Provider>\n  );\n});\nFloatingActionPanelRoot.displayName = \"FloatingActionPanelRoot\";\n\ninterface FloatingActionPanelTriggerProps {\n  children: React.ReactNode;\n  className?: string;\n  title: string;\n  mode: \"actions\" | \"note\";\n}\n\nconst FloatingActionPanelTrigger = React.forwardRef<\n  HTMLButtonElement,\n  FloatingActionPanelTriggerProps\n>(({ children, className, title, mode }, ref) => {\n  const { openPanel, uniqueId, setTitle } = useFloatingActionPanel();\n  const triggerRef = React.useRef<HTMLButtonElement>(null);\n\n  const handleClick = () => {\n    if (triggerRef.current) {\n      openPanel(triggerRef.current.getBoundingClientRect(), mode);\n      setTitle(title);\n    }\n  };\n\n  return (\n    <motion.button\n      ref={triggerRef}\n      layoutId={`floating-panel-trigger-${uniqueId}-${mode}`}\n      className={cn(\n        \"flex h-9 items-center rounded-md border border-zinc-200 bg-white px-3 text-sm font-medium text-zinc-900 shadow-sm hover:bg-zinc-50 dark:border-zinc-800 dark:bg-zinc-950 dark:text-zinc-50 dark:hover:bg-zinc-800\",\n        className\n      )}\n      onClick={handleClick}\n      whileHover={{ scale: 1.02 }}\n      whileTap={{ scale: 0.98 }}\n    >\n      {children}\n    </motion.button>\n  );\n});\nFloatingActionPanelTrigger.displayName = \"FloatingActionPanelTrigger\";\n\ninterface FloatingActionPanelContentProps {\n  children?: React.ReactNode;\n  className?: string;\n}\n\nconst FloatingActionPanelContent = React.forwardRef<\n  HTMLDivElement,\n  FloatingActionPanelContentProps\n>(({ children, className }, ref) => {\n  const { isOpen, closePanel, uniqueId, triggerRect, title, mode } =\n    useFloatingActionPanel();\n  const contentRef = React.useRef<HTMLDivElement>(null);\n\n  React.useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (\n        contentRef.current &&\n        !contentRef.current.contains(event.target as Node)\n      ) {\n        closePanel();\n      }\n    };\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => document.removeEventListener(\"mousedown\", handleClickOutside);\n  }, [closePanel]);\n\n  React.useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.key === \"Escape\") closePanel();\n    };\n    document.addEventListener(\"keydown\", handleKeyDown);\n    return () => document.removeEventListener(\"keydown\", handleKeyDown);\n  }, [closePanel]);\n\n  const getPosition = () => {\n    if (!triggerRect) return { left: \"50%\", top: \"50%\" };\n\n    // Get scroll position\n    const scrollX = window.scrollX || window.pageXOffset;\n    const scrollY = window.scrollY || window.pageYOffset;\n\n    // Calculate position\n    const left = triggerRect.left + scrollX;\n    const top = triggerRect.bottom + scrollY + 8; // 8px gap\n\n    return {\n      position: \"absolute\" as const,\n      left: 0,\n      top: 0,\n      transform: `translate3d(${left}px, ${top}px, 0)`,\n    };\n  };\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <div className=\"relative\">\n          <motion.div\n            initial={{ backdropFilter: \"blur(0px)\" }}\n            animate={{ backdropFilter: \"blur(4px)\" }}\n            exit={{ backdropFilter: \"blur(0px)\" }}\n            className=\"fixed inset-0 z-40 bg-black/5\"\n          />\n          <motion.div\n            ref={contentRef}\n            layoutId={`floating-panel-${uniqueId}-${mode}`}\n            className={cn(\n              \"absolute z-50 min-w-[200px] overflow-hidden rounded-lg border border-zinc-200 bg-white shadow-lg outline-none dark:border-zinc-800 dark:bg-zinc-950\",\n              className\n            )}\n            style={getPosition()}\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            exit={{ opacity: 0, scale: 0.9 }}\n          >\n            <div className=\"px-4 py-3 font-medium\">{title}</div>\n            {children}\n          </motion.div>\n        </div>\n      )}\n    </AnimatePresence>\n  );\n});\nFloatingActionPanelContent.displayName = \"FloatingActionPanelContent\";\n\ninterface FloatingActionPanelButtonProps {\n  children: React.ReactNode;\n  onClick?: () => void;\n  className?: string;\n}\n\nconst FloatingActionPanelButton = React.forwardRef<\n  HTMLButtonElement,\n  FloatingActionPanelButtonProps\n>(({ children, onClick, className }, ref) => {\n  return (\n    <motion.button\n      ref={ref}\n      className={cn(\n        \"flex w-full items-center gap-2 rounded-md px-2 py-1.5 text-sm text-zinc-900 hover:bg-zinc-100 dark:text-zinc-50 dark:hover:bg-zinc-800\",\n        className\n      )}\n      onClick={onClick}\n      whileTap={{ scale: 0.98 }}\n    >\n      {children}\n    </motion.button>\n  );\n});\nFloatingActionPanelButton.displayName = \"FloatingActionPanelButton\";\n\ninterface FloatingActionPanelFormProps {\n  children: React.ReactNode;\n  onSubmit?: (note: string) => void;\n  className?: string;\n}\n\nconst FloatingActionPanelForm = React.forwardRef<\n  HTMLFormElement,\n  FloatingActionPanelFormProps\n>(({ children, onSubmit, className }, ref) => {\n  const { note, closePanel } = useFloatingActionPanel();\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    onSubmit?.(note);\n    closePanel();\n  };\n\n  return (\n    <form\n      ref={ref}\n      className={cn(\"flex h-full flex-col\", className)}\n      onSubmit={handleSubmit}\n    >\n      {children}\n    </form>\n  );\n});\nFloatingActionPanelForm.displayName = \"FloatingActionPanelForm\";\n\ninterface FloatingActionPanelTextareaProps {\n  className?: string;\n  id?: string;\n}\n\nconst FloatingActionPanelTextarea = React.forwardRef<\n  HTMLTextAreaElement,\n  FloatingActionPanelTextareaProps\n>(({ className, id }, ref) => {\n  const { note, setNote } = useFloatingActionPanel();\n\n  return (\n    <textarea\n      ref={ref}\n      id={id}\n      className={cn(\n        \"h-full w-full resize-none rounded-md bg-transparent px-4 py-3 text-sm outline-none\",\n        className\n      )}\n      autoFocus\n      value={note}\n      onChange={(e) => setNote(e.target.value)}\n    />\n  );\n});\nFloatingActionPanelTextarea.displayName = \"FloatingActionPanelTextarea\";\n\nexport {\n  FloatingActionPanelRoot,\n  FloatingActionPanelTrigger,\n  FloatingActionPanelContent,\n  FloatingActionPanelButton,\n  FloatingActionPanelForm,\n  FloatingActionPanelTextarea,\n  FloatingActionPanelContext,\n};\n"}], "dependencies": ["framer-motion", "lucide-react"]}