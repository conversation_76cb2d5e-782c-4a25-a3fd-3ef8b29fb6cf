{"name": "display-cards", "type": "registry:ui", "code": "\"use client\";\n\nimport { cn } from \"@/lib/utils\";\nimport { AudioLines } from \"lucide-react\";\n\ninterface DisplayCardProps {\n  className?: string;\n  icon?: React.ReactNode;\n  title?: string;\n  description?: string;\n  date?: string;\n  iconClassName?: string;\n  titleClassName?: string;\n}\n\nfunction DisplayCard({\n  className,\n  icon = <AudioLines className=\"size-4 text-green-300\" />,\n  title = \"Featured\",\n  description = \"This is a skewed card with some text\",\n  date = \"Sep 23\",\n  iconClassName = \"text-green-500\",\n  titleClassName = \"text-green-500\",\n}: DisplayCardProps) {\n  return (\n    <div\n      className={cn(\n        \"relative flex h-36 w-[22rem] -skew-y-[8deg] select-none flex-col justify-between rounded-xl border-2 bg-muted/70 backdrop-blur-sm px-4 py-3 transition-all duration-700 after:absolute after:-right-1 after:top-[-5%] after:h-[110%] after:w-[20rem] after:bg-gradient-to-l after:from-background after:to-transparent after:content-[''] hover:border-white/20 hover:bg-muted [&>*]:flex [&>*]:items-center [&>*]:gap-2\",\n        className\n      )}\n    >\n      <div>\n        <span className=\"relative inline-block rounded-full bg-green-800 p-1\">\n          {icon}\n        </span>\n        <p className={cn(\"text-lg\", titleClassName)}>{title}</p>\n      </div>\n      <p className=\"whitespace-nowrap text-lg\">{description}</p>\n      <p className=\"text-muted-foreground\">{date}</p>\n    </div>\n  );\n}\n\ninterface DisplayCardsProps {\n  cards?: DisplayCardProps[];\n}\n\nexport default function DisplayCards({ cards }: DisplayCardsProps) {\n  const defaultCards = [\n    {\n      className: \"[grid-area:stack] hover:-translate-y-10 before:absolute before:w-[100%] before:outline-1 before:rounded-xl before:outline-border before:h-[100%] before:content-[''] before:bg-blend-overlay before:bg-background/50 grayscale-[100%] hover:before:opacity-0 before:transition-opacity before:duration:700 hover:grayscale-0 before:left-0 before:top-0\",\n    },\n    {\n      className: \"[grid-area:stack] translate-x-16 translate-y-10 hover:-translate-y-10 before:absolute before:w-[100%] before:outline-1 before:rounded-xl before:outline-border before:h-[100%] before:content-[''] before:bg-blend-overlay before:bg-background/50 grayscale-[100%] hover:before:opacity-0 before:transition-opacity before:duration:700 hover:grayscale-0 before:left-0 before:top-0\",\n    },\n    {\n      className: \"[grid-area:stack] translate-x-32 translate-y-20 hover:translate-y-10\",\n    },\n  ];\n\n  const displayCards = cards || defaultCards;\n\n  return (\n    <div className=\"grid [grid-template-areas:'stack'] place-items-center opacity-100 animate-in fade-in-0 duration-700\">\n      {displayCards.map((cardProps, index) => (\n        <DisplayCard key={index} {...cardProps} />\n      ))}\n    </div>\n  );\n}", "files": [{"path": "components/prismui/display-cards.tsx", "type": "registry:ui", "content": "\"use client\";\n\nimport { cn } from \"@/lib/utils\";\nimport { Sparkles } from \"lucide-react\";\n\ninterface DisplayCardProps {\n  className?: string;\n  icon?: React.ReactNode;\n  title?: string;\n  description?: string;\n  date?: string;\n  iconClassName?: string;\n  titleClassName?: string;\n}\n\nfunction DisplayCard({\n  className,\n  icon = <Sparkles className=\"size-4 text-blue-300\" />,\n  title = \"Featured\",\n  description = \"Discover amazing content\",\n  date = \"Just now\",\n  iconClassName = \"text-blue-500\",\n  titleClassName = \"text-blue-500\",\n}: DisplayCardProps) {\n  return (\n    <div\n      className={cn(\n        \"relative flex h-44 w-[26rem] -skew-y-[8deg] select-none flex-col justify-between rounded-xl border-2 bg-muted/70 backdrop-blur-sm px-6 py-4 transition-all duration-700 after:absolute after:-right-1 after:top-[-5%] after:h-[110%] after:w-[20rem] after:bg-gradient-to-l after:from-background after:to-transparent after:content-[''] hover:border-white/20 hover:bg-muted [&>*]:flex [&>*]:items-center [&>*]:gap-2\",\n        className\n      )}\n    >\n      <div>\n        <span className=\"relative inline-block rounded-full bg-blue-800 p-1.5\">\n          {icon}\n        </span>\n        <p className={cn(\"text-lg font-medium\", titleClassName)}>{title}</p>\n      </div>\n      <p className=\"whitespace-nowrap text-lg\">{description}</p>\n      <p className=\"text-muted-foreground\">{date}</p>\n    </div>\n  );\n}\n\ninterface DisplayCardsProps {\n  cards?: DisplayCardProps[];\n}\n\nexport default function DisplayCards({ cards }: DisplayCardsProps) {\n  const defaultCards = [\n    {\n      icon: <Sparkles className=\"size-4 text-blue-300\" />,\n      title: \"Featured\",\n      description: \"Discover amazing content\",\n      date: \"Just now\",\n      iconClassName: \"text-blue-500\",\n      titleClassName: \"text-blue-500\",\n      className:\n        \"[grid-area:stack] hover:-translate-y-10 before:absolute before:w-[100%] before:outline-1 before:rounded-xl before:outline-border before:h-[100%] before:content-[''] before:bg-blend-overlay before:bg-background/50 grayscale-[100%] hover:before:opacity-0 before:transition-opacity before:duration:700 hover:grayscale-0 before:left-0 before:top-0\",\n    },\n    {\n      icon: <Sparkles className=\"size-4 text-blue-300\" />,\n      title: \"Popular\",\n      description: \"Trending this week\",\n      date: \"2 days ago\",\n      iconClassName: \"text-blue-500\",\n      titleClassName: \"text-blue-500\",\n      className:\n        \"[grid-area:stack] translate-x-12 translate-y-10 hover:-translate-y-1 before:absolute before:w-[100%] before:outline-1 before:rounded-xl before:outline-border before:h-[100%] before:content-[''] before:bg-blend-overlay before:bg-background/50 grayscale-[100%] hover:before:opacity-0 before:transition-opacity before:duration:700 hover:grayscale-0 before:left-0 before:top-0\",\n    },\n    {\n      icon: <Sparkles className=\"size-4 text-blue-300\" />,\n      title: \"New\",\n      description: \"Latest updates and features\",\n      date: \"Today\",\n      iconClassName: \"text-blue-500\",\n      titleClassName: \"text-blue-500\",\n      className:\n        \"[grid-area:stack] translate-x-24 translate-y-20 hover:translate-y-10\",\n    },\n  ];\n\n  const displayCards = cards || defaultCards;\n\n  return (\n    <div className=\"grid [grid-template-areas:'stack'] place-items-center opacity-100 animate-in fade-in-0 duration-700 -ml-12\">\n      {displayCards.map((cardProps, index) => (\n        <DisplayCard key={index} {...cardProps} />\n      ))}\n    </div>\n  );\n}\n"}], "dependencies": ["lucide-react"]}