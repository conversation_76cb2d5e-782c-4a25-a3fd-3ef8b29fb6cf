{"name": "hero-test", "type": "registry:block", "code": "\"use client\";\n\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { WordReveal } from \"@/components/prismui/word-reveal\";\n\nexport function HeroTest() {\n  return (\n    <section className=\"container flex flex-col items-center justify-center gap-4 pb-8 pt-6 md:py-10\">\n      <div className=\"flex max-w-[980px] flex-col items-center gap-2\">\n        <h1 className=\"text-center text-3xl font-bold leading-tight tracking-tighter md:text-6xl lg:leading-[1.1]\">\n          <WordReveal>\n            Building blocks for your Next.js project\n          </WordReveal>\n        </h1>\n        <p className=\"max-w-[750px] text-center text-lg text-muted-foreground sm:text-xl\">\n          Beautifully designed components that you can copy and paste into your\n          apps. Accessible. Customizable. Open Source.\n        </p>\n      </div>\n      <div className=\"flex gap-4\">\n        <Button size=\"lg\">Get Started</Button>\n        <Button size=\"lg\" variant=\"outline\">\n          Components\n        </Button>\n      </div>\n    </section>\n  );\n}", "files": [{"path": "registry/section/hero-test.tsx", "type": "registry:block", "content": "\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { cn } from \"@/lib/utils\";\nimport { Button } from \"@/components/ui/button\";\nimport WordReveal from \"@/components/prismui/word-reveal\";\n\ninterface HeroProps {\n  className?: string;\n  children?: React.ReactNode;\n}\n\nexport function HeroTest({ className, children }: HeroProps) {\n  return (\n    <div\n      className={cn(\n        \"relative min-h-[600px] flex flex-col items-center justify-center overflow-hidden bg-black pt-32\",\n        className\n      )}\n    >\n      {/* Gradient Background */}\n      <div className=\"absolute inset-0 w-full h-full bg-gradient-to-b from-black via-zinc-900/50 to-black\" />\n\n      {/* Animated Gradient Blob */}\n      <motion.div\n        className=\"absolute w-[1000px] h-[1000px] rounded-full bg-gradient-to-r from-violet-500/30 to-fuchsia-500/30 blur-3xl\"\n        animate={{\n          scale: [1, 1.1, 1],\n          opacity: [0.3, 0.2, 0.3],\n          x: [0, 100, 0],\n          y: [0, 50, 0],\n        }}\n        transition={{\n          duration: 8,\n          repeat: Infinity,\n          ease: \"linear\",\n        }}\n      />\n\n      {/* Content Container */}\n      <div className=\"relative z-10 max-w-5xl mx-auto px-4 text-center\">\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ duration: 1 }}\n        >\n          <WordReveal\n            text=\"Linear is a better way to build products\"\n            className=\"mb-6\"\n            delay={0.2}\n          />\n\n          <motion.p\n            initial={{ opacity: 0, y: 20, filter: \"blur(10px)\" }}\n            animate={{ opacity: 1, y: 0, filter: \"blur(0px)\" }}\n            transition={{ duration: 1, delay: 2.5 }}\n            className=\"text-lg md:text-xl text-zinc-400 mb-8 max-w-2xl mx-auto\"\n          >\n            Meet the new standard for modern software development. Streamline\n            issues, sprints, and product roadmaps.\n          </motion.p>\n\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 1, delay: 3 }}\n            className=\"flex flex-col sm:flex-row gap-4 justify-center\"\n          >\n            <Button\n              size=\"lg\"\n              className=\"bg-white text-black hover:bg-zinc-200 transition-colors\"\n            >\n              Get Started\n            </Button>\n            <Button\n              size=\"lg\"\n              variant=\"outline\"\n              className=\"border-zinc-700 text-white hover:bg-zinc-800 transition-colors\"\n            >\n              View Demo\n            </Button>\n          </motion.div>\n        </motion.div>\n\n        {/* 3D Card Animation */}\n        <motion.div\n          className=\"mt-16 relative\"\n          initial={{ opacity: 0, y: 100 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 1, delay: 3.5 }}\n        >\n          <div className=\"relative w-full aspect-[16/9] bg-gradient-to-br from-zinc-800 to-zinc-900 rounded-xl overflow-hidden border border-zinc-800\">\n            <motion.div\n              className=\"absolute inset-0 bg-gradient-to-br from-violet-500/10 to-fuchsia-500/10\"\n              animate={{\n                opacity: [0.5, 0.3, 0.5],\n              }}\n              transition={{\n                duration: 4,\n                repeat: Infinity,\n                ease: \"linear\",\n              }}\n            />\n            {children}\n          </div>\n\n          {/* Reflection Effect */}\n          <div className=\"absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent\" />\n        </motion.div>\n      </div>\n    </div>\n  );\n}\n"}], "dependencies": ["@/components/ui/button", "@/components/prismui/word-reveal"]}