{"name": "number-ticker", "type": "registry:ui", "code": "\"use client\";\n\nimport { useEffect, useRef, useState } from \"react\";\nimport { useInView } from \"framer-motion\";\nimport NumberFlow, { type Format } from \"@number-flow/react\";\nimport { cn } from \"@/lib/utils\";\n\ninterface NumberTickerProps {\n  value: number;\n  direction?: \"up\" | \"down\";\n  className?: string;\n  delay?: number; // delay in s\n  decimalPlaces?: number;\n  format?: Format;\n}\n\nexport default function NumberTicker({\n  value,\n  direction = \"up\",\n  delay = 0,\n  className,\n  decimalPlaces = 0,\n  format,\n}: NumberTickerProps) {\n  const ref = useRef<HTMLSpanElement>(null);\n  const isInView = useInView(ref, { once: true, margin: \"0px\" });\n  const [currentValue, setCurrentValue] = useState(direction === \"down\" ? value : 0);\n\n  useEffect(() => {\n    if (isInView) {\n      const timer = setTimeout(() => {\n        setCurrentValue(direction === \"down\" ? 0 : value);\n      }, delay * 1000);\n      return () => clearTimeout(timer);\n    }\n  }, [isInView, delay, value, direction]);\n\n  const defaultFormat: Format = {\n    minimumFractionDigits: decimalPlaces,\n    maximumFractionDigits: decimalPlaces,\n    notation: \"standard\",\n  };\n\n  return (\n    <span\n      className={cn(\n        \"inline-block tabular-nums tracking-wider text-black dark:text-white\",\n        className\n      )}\n      ref={ref}\n    >\n      {isInView && (\n        <NumberFlow\n          value={currentValue}\n          format={format || defaultFormat}\n          transformTiming={{\n            duration: 1000,\n            easing: \"ease-out\",\n          }}\n          willChange\n        />\n      )}\n    </span>\n  );\n}", "files": [{"path": "components/prismui/number-ticker.tsx", "type": "registry:ui", "content": "\"use client\";\n\nimport { useEffect, useRef, useState } from \"react\";\nimport { useInView } from \"framer-motion\";\nimport NumberFlow, { type Format } from \"@number-flow/react\";\nimport { cn } from \"@/lib/utils\";\n\ninterface NumberTickerProps {\n  value: number;\n  direction?: \"up\" | \"down\";\n  className?: string;\n  delay?: number; // delay in s\n  decimalPlaces?: number;\n  format?: Format;\n}\n\nexport default function NumberTicker({\n  value,\n  direction = \"up\",\n  delay = 0,\n  className,\n  decimalPlaces = 0,\n  format,\n}: NumberTickerProps) {\n  const ref = useRef<HTMLSpanElement>(null);\n  const isInView = useInView(ref, { once: true, margin: \"0px\" });\n  const [currentValue, setCurrentValue] = useState(\n    direction === \"down\" ? value : 0\n  );\n\n  useEffect(() => {\n    if (isInView) {\n      const timer = setTimeout(() => {\n        setCurrentValue(direction === \"down\" ? 0 : value);\n      }, delay * 1000);\n      return () => clearTimeout(timer);\n    }\n  }, [isInView, delay, value, direction]);\n\n  const defaultFormat: Format = {\n    minimumFractionDigits: decimalPlaces,\n    maximumFractionDigits: decimalPlaces,\n    notation: \"standard\",\n  };\n\n  return (\n    <span\n      className={cn(\n        \"inline-block tabular-nums tracking-wider text-black dark:text-white\",\n        className\n      )}\n      ref={ref}\n    >\n      {isInView && (\n        <NumberFlow\n          value={currentValue}\n          format={format || defaultFormat}\n          transformTiming={{\n            duration: 1000,\n            easing: \"ease-out\",\n          }}\n          willChange\n        />\n      )}\n    </span>\n  );\n}\n"}], "dependencies": ["@number-flow/react", "framer-motion", "@/lib/utils"]}