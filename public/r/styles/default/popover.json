{"name": "popover", "type": "registry:ui", "code": "\"use client\";\n\nimport * as React from \"react\";\nimport { AnimatePresence, MotionConfig, motion } from \"framer-motion\";\nimport { cn } from \"@/lib/utils\";\nimport { Button } from \"@/components/ui/button\";\n\nconst TRANSITION = {\n  type: \"spring\",\n  bounce: 0.1,\n  duration: 0.4,\n};\n\ninterface PopoverContextType {\n  isOpen: boolean;\n  openPopover: () => void;\n  closePopover: () => void;\n  uniqueId: string;\n  note: string;\n  setNote: (note: string) => void;\n}\n\nconst PopoverContext = React.createContext<PopoverContextType | undefined>(\n  undefined\n);\n\nfunction usePopoverLogic() {\n  const uniqueId = React.useId();\n  const [isOpen, setIsOpen] = React.useState(false);\n  const [note, setNote] = React.useState(\"\");\n\n  const openPopover = () => setIsOpen(true);\n  const closePopover = () => {\n    setIsOpen(false);\n    setNote(\"\");\n  };\n\n  return {\n    isOpen,\n    openPopover,\n    closePopover,\n    uniqueId,\n    note,\n    setNote,\n  };\n}\n\ninterface PopoverRootProps {\n  children: React.ReactNode;\n}\n\nexport function PopoverRoot({ children }: PopoverRootProps) {\n  const popoverLogic = usePopoverLogic();\n\n  return (\n    <PopoverContext.Provider value={popoverLogic}>\n      <MotionConfig transition={TRANSITION}>\n        <div className=\"relative\">{children}</div>\n      </MotionConfig>\n    </PopoverContext.Provider>\n  );\n}\n\ninterface PopoverTriggerProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: \"default\" | \"outline\" | \"secondary\" | \"ghost\";\n}\n\nexport function PopoverTrigger({\n  children,\n  className,\n  variant = \"default\",\n  ...props\n}: PopoverTriggerProps) {\n  const { openPopover, uniqueId } = React.useContext(PopoverContext)!;\n\n  return (\n    <motion.div layoutId={`popover-trigger-${uniqueId}`}>\n      <Button\n        variant={variant}\n        className={className}\n        onClick={openPopover}\n        {...props}\n      >\n        {children}\n      </Button>\n    </motion.div>\n  );\n}\n\ninterface PopoverContentProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport function PopoverContent({\n  children,\n  className,\n}: PopoverContentProps) {\n  const { isOpen, closePopover, uniqueId } = React.useContext(PopoverContext)!;\n  const contentRef = React.useRef<HTMLDivElement>(null);\n\n  React.useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (\n        contentRef.current &&\n        !contentRef.current.contains(event.target as Node)\n      ) {\n        closePopover();\n      }\n    };\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => document.removeEventListener(\"mousedown\", handleClickOutside);\n  }, [closePopover]);\n\n  React.useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.key === \"Escape\") closePopover();\n    };\n    document.addEventListener(\"keydown\", handleKeyDown);\n    return () => document.removeEventListener(\"keydown\", handleKeyDown);\n  }, [closePopover]);\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <>\n          <motion.div\n            initial={{ backdropFilter: \"blur(0px)\" }}\n            animate={{ backdropFilter: \"blur(4px)\" }}\n            exit={{ backdropFilter: \"blur(0px)\" }}\n            className=\"fixed inset-0 z-40 bg-black/5\"\n          />\n          <motion.div\n            ref={contentRef}\n            layoutId={`popover-${uniqueId}`}\n            className={cn(\n              \"fixed z-50 min-w-[200px] overflow-hidden rounded-lg border border-border bg-background shadow-lg outline-none\",\n              className\n            )}\n            style={{\n              left: \"50%\",\n              top: \"50%\",\n              transform: \"translate(-50%, -50%)\",\n            }}\n            initial={{ opacity: 0, scale: 0.9, y: -8 }}\n            animate={{ opacity: 1, scale: 1, y: 0 }}\n            exit={{ opacity: 0, scale: 0.9, y: -8 }}\n          >\n            {children}\n          </motion.div>\n        </>\n      )}\n    </AnimatePresence>\n  );\n}\n\ninterface PopoverFormProps {\n  children: React.ReactNode;\n  onSubmit?: (note: string) => void;\n}\n\nexport function PopoverForm({ children, onSubmit }: PopoverFormProps) {\n  const { note, closePopover } = React.useContext(PopoverContext)!;\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    onSubmit?.(note);\n    closePopover();\n  };\n\n  return (\n    <form className=\"flex h-full flex-col\" onSubmit={handleSubmit}>\n      {children}\n    </form>\n  );\n}\n\ninterface PopoverLabelProps {\n  children: React.ReactNode;\n}\n\nexport function PopoverLabel({ children }: PopoverLabelProps) {\n  return <div className=\"px-4 py-3 font-medium\">{children}</div>;\n}\n\ninterface PopoverTextareaProps {\n  className?: string;\n  id?: string;\n}\n\nexport function PopoverTextarea({ className, id }: PopoverTextareaProps) {\n  const { note, setNote } = React.useContext(PopoverContext)!;\n\n  return (\n    <textarea\n      id={id}\n      className={cn(\n        \"h-full w-full resize-none rounded-md bg-transparent px-4 py-3 text-sm outline-none\",\n        className\n      )}\n      autoFocus\n      value={note}\n      onChange={(e) => setNote(e.target.value)}\n    />\n  );\n}\n\ninterface PopoverFooterProps {\n  children: React.ReactNode;\n}\n\nexport function PopoverFooter({ children }: PopoverFooterProps) {\n  return (\n    <div className=\"flex items-center justify-between gap-2 border-t p-3\">\n      {children}\n    </div>\n  );\n}\n\nexport function PopoverCloseButton() {\n  const { closePopover } = React.useContext(PopoverContext)!;\n\n  return (\n    <Button variant=\"ghost\" size=\"sm\" onClick={closePopover}>\n      Cancel\n    </Button>\n  );\n}\n\ninterface PopoverSubmitButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: \"default\" | \"outline\" | \"secondary\" | \"ghost\";\n}\n\nexport function PopoverSubmitButton({\n  children,\n  variant = \"default\",\n  ...props\n}: PopoverSubmitButtonProps) {\n  return (\n    <Button type=\"submit\" variant={variant} size=\"sm\" {...props}>\n      {children}\n    </Button>\n  );\n}\n\ninterface PopoverHeaderProps {\n  children: React.ReactNode;\n}\n\nexport function PopoverHeader({ children }: PopoverHeaderProps) {\n  return <div className=\"px-4 py-3\">{children}</div>;\n}\n\ninterface PopoverBodyProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport function PopoverBody({ children, className }: PopoverBodyProps) {\n  return <div className={cn(\"px-2 py-1.5\", className)}>{children}</div>;\n}\n\ninterface PopoverButtonProps {\n  children: React.ReactNode;\n  onClick?: () => void;\n  className?: string;\n}\n\nexport function PopoverButton({\n  children,\n  onClick,\n  className,\n}: PopoverButtonProps) {\n  return (\n    <button\n      className={cn(\n        \"flex w-full items-center gap-2 rounded-md px-2 py-1.5 text-sm text-foreground hover:bg-muted\",\n        className\n      )}\n      onClick={onClick}\n    >\n      {children}\n    </button>\n  );\n}", "files": [{"path": "components/prismui/popover.tsx", "type": "registry:ui", "content": "\"use client\";\n\nimport * as React from \"react\";\nimport { AnimatePresence, MotionConfig, motion } from \"framer-motion\";\nimport { X } from \"lucide-react\";\nimport { cn } from \"@/lib/utils\";\nimport { Button } from \"@/components/ui/button\";\n\nconst TRANSITION = {\n  type: \"spring\",\n  bounce: 0.05,\n  duration: 0.3,\n};\n\ninterface PopoverContextType {\n  isOpen: boolean;\n  openPopover: () => void;\n  closePopover: () => void;\n  uniqueId: string;\n  note: string;\n  setNote: (note: string) => void;\n}\n\nconst PopoverContext = React.createContext<PopoverContextType | undefined>(\n  undefined\n);\n\nfunction usePopover() {\n  const context = React.useContext(PopoverContext);\n  if (!context) {\n    throw new Error(\"usePopover must be used within a PopoverProvider\");\n  }\n  return context;\n}\n\nfunction usePopoverLogic() {\n  const uniqueId = React.useId();\n  const [isOpen, setIsOpen] = React.useState(false);\n  const [note, setNote] = React.useState(\"\");\n\n  const openPopover = () => setIsOpen(true);\n  const closePopover = () => {\n    setIsOpen(false);\n    setNote(\"\");\n  };\n\n  return { isOpen, openPopover, closePopover, uniqueId, note, setNote };\n}\n\ninterface PopoverRootProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst PopoverRoot = React.forwardRef<HTMLDivElement, PopoverRootProps>(\n  ({ children, className }, ref) => {\n    const popoverLogic = usePopoverLogic();\n\n    return (\n      <PopoverContext.Provider value={popoverLogic}>\n        <MotionConfig transition={TRANSITION}>\n          <div\n            ref={ref}\n            className={cn(\n              \"relative flex items-center justify-center isolate\",\n              className\n            )}\n          >\n            {children}\n          </div>\n        </MotionConfig>\n      </PopoverContext.Provider>\n    );\n  }\n);\nPopoverRoot.displayName = \"PopoverRoot\";\n\ninterface PopoverTriggerProps {\n  children: React.ReactNode;\n  className?: string;\n  variant?:\n    | \"default\"\n    | \"destructive\"\n    | \"outline\"\n    | \"secondary\"\n    | \"ghost\"\n    | \"link\";\n}\n\nconst PopoverTrigger = React.forwardRef<HTMLButtonElement, PopoverTriggerProps>(\n  ({ children, className, variant = \"outline\" }, ref) => {\n    const { openPopover, uniqueId } = usePopover();\n\n    return (\n      <motion.div key=\"button\" layoutId={`popover-${uniqueId}`}>\n        <Button\n          ref={ref}\n          variant={variant}\n          className={className}\n          onClick={openPopover}\n        >\n          <motion.span\n            layoutId={`popover-label-${uniqueId}`}\n            className=\"text-sm\"\n          >\n            {children}\n          </motion.span>\n        </Button>\n      </motion.div>\n    );\n  }\n);\nPopoverTrigger.displayName = \"PopoverTrigger\";\n\ninterface PopoverContentProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst PopoverContent = React.forwardRef<HTMLDivElement, PopoverContentProps>(\n  ({ children, className }, ref) => {\n    const { isOpen, closePopover, uniqueId } = usePopover();\n    const contentRef = React.useRef<HTMLDivElement>(null);\n\n    React.useEffect(() => {\n      const handleClickOutside = (event: MouseEvent) => {\n        if (\n          contentRef.current &&\n          !contentRef.current.contains(event.target as Node)\n        ) {\n          closePopover();\n        }\n      };\n      document.addEventListener(\"mousedown\", handleClickOutside);\n      return () =>\n        document.removeEventListener(\"mousedown\", handleClickOutside);\n    }, [closePopover]);\n\n    React.useEffect(() => {\n      const handleKeyDown = (event: KeyboardEvent) => {\n        if (event.key === \"Escape\") closePopover();\n      };\n      document.addEventListener(\"keydown\", handleKeyDown);\n      return () => document.removeEventListener(\"keydown\", handleKeyDown);\n    }, [closePopover]);\n\n    return (\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            ref={contentRef}\n            layoutId={`popover-${uniqueId}`}\n            className={cn(\n              \"absolute z-50 min-w-[200px] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md outline-none\",\n              className\n            )}\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            exit={{ opacity: 0, scale: 0.9 }}\n          >\n            {children}\n          </motion.div>\n        )}\n      </AnimatePresence>\n    );\n  }\n);\nPopoverContent.displayName = \"PopoverContent\";\n\ninterface PopoverFormProps {\n  children: React.ReactNode;\n  onSubmit?: (note: string) => void;\n  className?: string;\n}\n\nconst PopoverForm = React.forwardRef<HTMLFormElement, PopoverFormProps>(\n  ({ children, onSubmit, className }, ref) => {\n    const { note, closePopover } = usePopover();\n\n    const handleSubmit = (e: React.FormEvent) => {\n      e.preventDefault();\n      onSubmit?.(note);\n      closePopover();\n    };\n\n    return (\n      <form\n        ref={ref}\n        className={cn(\"flex h-full flex-col\", className)}\n        onSubmit={handleSubmit}\n      >\n        {children}\n      </form>\n    );\n  }\n);\nPopoverForm.displayName = \"PopoverForm\";\n\ninterface PopoverLabelProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst PopoverLabel = React.forwardRef<HTMLSpanElement, PopoverLabelProps>(\n  ({ children, className }, ref) => {\n    const { uniqueId, note } = usePopover();\n\n    return (\n      <motion.span\n        ref={ref}\n        layoutId={`popover-label-${uniqueId}`}\n        aria-hidden=\"true\"\n        style={{\n          opacity: note ? 0 : 1,\n        }}\n        className={cn(\n          \"absolute left-4 top-3 select-none text-sm text-muted-foreground\",\n          className\n        )}\n      >\n        {children}\n      </motion.span>\n    );\n  }\n);\nPopoverLabel.displayName = \"PopoverLabel\";\n\ninterface PopoverTextareaProps {\n  className?: string;\n  id?: string;\n}\n\nconst PopoverTextarea = React.forwardRef<\n  HTMLTextAreaElement,\n  PopoverTextareaProps\n>(({ className, id }, ref) => {\n  const { note, setNote } = usePopover();\n\n  return (\n    <textarea\n      ref={ref}\n      id={id}\n      className={cn(\n        \"h-full w-full resize-none rounded-md bg-transparent px-4 py-3 text-sm outline-none placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      autoFocus\n      value={note}\n      onChange={(e) => setNote(e.target.value)}\n    />\n  );\n});\nPopoverTextarea.displayName = \"PopoverTextarea\";\n\ninterface PopoverFooterProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst PopoverFooter = React.forwardRef<HTMLDivElement, PopoverFooterProps>(\n  ({ children, className }, ref) => {\n    return (\n      <div\n        ref={ref}\n        key=\"close\"\n        className={cn(\n          \"flex items-center justify-between border-t bg-muted/50 px-4 py-3\",\n          className\n        )}\n      >\n        {children}\n      </div>\n    );\n  }\n);\nPopoverFooter.displayName = \"PopoverFooter\";\n\ninterface PopoverCloseButtonProps {\n  className?: string;\n}\n\nconst PopoverCloseButton = React.forwardRef<\n  HTMLButtonElement,\n  PopoverCloseButtonProps\n>(({ className }, ref) => {\n  const { closePopover } = usePopover();\n\n  return (\n    <Button\n      ref={ref}\n      type=\"button\"\n      variant=\"ghost\"\n      size=\"icon\"\n      className={cn(\"h-8 w-8\", className)}\n      onClick={closePopover}\n      aria-label=\"Close popover\"\n    >\n      <X className=\"h-4 w-4\" />\n    </Button>\n  );\n});\nPopoverCloseButton.displayName = \"PopoverCloseButton\";\n\ninterface PopoverSubmitButtonProps {\n  children?: React.ReactNode;\n  className?: string;\n  variant?:\n    | \"default\"\n    | \"destructive\"\n    | \"outline\"\n    | \"secondary\"\n    | \"ghost\"\n    | \"link\";\n}\n\nconst PopoverSubmitButton = React.forwardRef<\n  HTMLButtonElement,\n  PopoverSubmitButtonProps\n>(({ children = \"Submit\", className, variant = \"default\" }, ref) => {\n  return (\n    <Button\n      ref={ref}\n      type=\"submit\"\n      variant={variant}\n      size=\"sm\"\n      className={className}\n      aria-label=\"Submit note\"\n    >\n      {children}\n    </Button>\n  );\n});\nPopoverSubmitButton.displayName = \"PopoverSubmitButton\";\n\ninterface PopoverHeaderProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst PopoverHeader = React.forwardRef<HTMLDivElement, PopoverHeaderProps>(\n  ({ children, className }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          \"border-b px-4 py-2.5 font-medium text-foreground/90\",\n          className\n        )}\n      >\n        {children}\n      </div>\n    );\n  }\n);\nPopoverHeader.displayName = \"PopoverHeader\";\n\ninterface PopoverBodyProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst PopoverBody = React.forwardRef<HTMLDivElement, PopoverBodyProps>(\n  ({ children, className }, ref) => {\n    return (\n      <div ref={ref} className={cn(\"p-4\", className)}>\n        {children}\n      </div>\n    );\n  }\n);\nPopoverBody.displayName = \"PopoverBody\";\n\ninterface PopoverButtonProps {\n  children: React.ReactNode;\n  onClick?: () => void;\n  className?: string;\n}\n\nconst PopoverButton = React.forwardRef<HTMLButtonElement, PopoverButtonProps>(\n  ({ children, onClick, className }, ref) => {\n    return (\n      <Button\n        ref={ref}\n        variant=\"ghost\"\n        className={cn(\n          \"w-full justify-start gap-2 px-4 py-2 font-normal\",\n          className\n        )}\n        onClick={onClick}\n      >\n        {children}\n      </Button>\n    );\n  }\n);\nPopoverButton.displayName = \"PopoverButton\";\n\nexport {\n  PopoverRoot,\n  PopoverTrigger,\n  PopoverContent,\n  PopoverForm,\n  PopoverLabel,\n  PopoverTextarea,\n  PopoverFooter,\n  PopoverCloseButton,\n  PopoverSubmitButton,\n  PopoverHeader,\n  PopoverBody,\n  PopoverButton,\n};\n"}], "dependencies": ["framer-motion"]}