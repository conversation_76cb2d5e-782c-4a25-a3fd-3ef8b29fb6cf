{"name": "hero", "type": "registry:ui", "code": "\"use client\";\n\nimport { motion, useAnimation } from \"framer-motion\";\nimport { Icons } from \"@/components/icons\";\nimport { buttonVariants } from \"@/components/ui/button\";\nimport { cn } from \"@/lib/utils\";\nimport Link from \"next/link\";\n\nconst ease = [0.16, 1, 0.3, 1];\n\ninterface HeroPillProps {\n  href?: string;\n  text: string;\n  icon?: React.ReactNode;\n  endIcon?: React.ReactNode;\n}\n\nfunction HeroPill({ href, text, icon, endIcon }: HeroPillProps) {\n  const controls = useAnimation();\n\n  return (\n    <Link href={href || \"/docs\"} className=\"group\">\n      <motion.div\n        className=\"inline-flex items-center gap-2 rounded-full border bg-background px-4 py-1.5 text-sm transition-colors hover:bg-muted\"\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.8, ease }}\n        onHoverStart={() => controls.start({ rotate: -10 })}\n        onHoverEnd={() => controls.start({ rotate: 0 })}\n      >\n        <motion.div\n          className=\"text-foreground/60 transition-colors group-hover:text-primary\"\n          animate={controls}\n          transition={{ type: \"spring\", stiffness: 300, damping: 10 }}\n        >\n          {icon || <Icons.logo className=\"h-4 w-4\" />}\n        </motion.div>\n        <span>{text}</span>\n        {endIcon || <Icons.chevronRight className=\"h-4 w-4\" />}\n      </motion.div>\n    </Link>\n  );\n}\n\ninterface HeroContentProps {\n  title: string;\n  titleHighlight?: string;\n  description: string;\n  primaryAction?: {\n    href: string;\n    text: string;\n    icon?: React.ReactNode;\n  };\n  secondaryAction?: {\n    href: string;\n    text: string;\n    icon?: React.ReactNode;\n  };\n}\n\nfunction HeroContent({\n  title,\n  titleHighlight,\n  description,\n  primaryAction,\n  secondaryAction,\n}: HeroContentProps) {\n  return (\n    <div className=\"flex flex-col space-y-4\">\n      <motion.h1\n        className=\"text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl xl:text-8xl\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.8, ease }}\n      >\n        {title}{\" \"}\n        {titleHighlight && <span className=\"text-primary\">{titleHighlight}</span>}\n      </motion.h1>\n      <motion.p\n        className=\"max-w-[42rem] leading-normal text-muted-foreground sm:text-xl sm:leading-8\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.1, duration: 0.8, ease }}\n      >\n        {description}\n      </motion.p>\n      <motion.div\n        className=\"flex flex-col sm:flex-row gap-4 pt-4\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.2, duration: 0.8, ease }}\n      >\n        {primaryAction && (\n          <Link\n            href={primaryAction.href}\n            className={cn(\n              buttonVariants({ size: \"lg\" }),\n              \"gap-2 w-full sm:w-auto justify-center\"\n            )}\n          >\n            {primaryAction.icon}\n            {primaryAction.text}\n          </Link>\n        )}\n        {secondaryAction && (\n          <Link\n            href={secondaryAction.href}\n            className={cn(\n              buttonVariants({ variant: \"outline\", size: \"lg\" }),\n              \"gap-2 w-full sm:w-auto justify-center\"\n            )}\n          >\n            {secondaryAction.icon}\n            {secondaryAction.text}\n          </Link>\n        )}\n      </motion.div>\n    </div>\n  );\n}\n\ninterface HeroProps {\n  pill?: {\n    href?: string;\n    text: string;\n    icon?: React.ReactNode;\n    endIcon?: React.ReactNode;\n  };\n  content: HeroContentProps;\n  preview?: React.ReactNode;\n  className?: string;\n}\n\nexport default function Hero({ pill, content, preview, className }: HeroProps) {\n  return (\n    <div className={cn(\"container relative overflow-hidden\", className)}>\n      <div className=\"flex min-h-[calc(100vh-64px)] flex-col lg:flex-row items-center py-8 px-4 md:px-8 lg:px-12\">\n        <div className=\"flex flex-col gap-4 w-full lg:max-w-2xl\">\n          {pill && <HeroPill {...pill} />}\n          <HeroContent {...content} />\n        </div>\n        {preview && (\n          <div className=\"w-full lg:max-w-xl lg:pl-16 mt-12 lg:mt-0\">\n            {preview}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}", "files": [{"path": "components/prismui/hero.tsx", "type": "registry:ui", "content": "\"use client\";\n\nimport { motion, useAnimation } from \"framer-motion\";\nimport { Icons } from \"@/components/icons\";\nimport { buttonVariants } from \"@/components/ui/button\";\nimport { cn } from \"@/lib/utils\";\nimport Link from \"next/link\";\nimport { ComponentPreview } from \"@/components/sections/component-preview\";\nimport { useEffect } from \"react\";\n\nconst ease = [0.16, 1, 0.3, 1];\n\ninterface HeroPillProps {\n  href?: string;\n  text: string;\n  icon?: React.ReactNode;\n  endIcon?: React.ReactNode;\n}\n\nfunction HeroPill({ href, text, icon, endIcon }: HeroPillProps) {\n  const controls = useAnimation();\n\n  return (\n    <Link href={href || \"/docs\"} className=\"group\">\n      <motion.div\n        className=\"inline-flex items-center gap-2 rounded-full border bg-background px-4 py-1.5 text-sm transition-colors hover:bg-muted\"\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.8, ease }}\n        onHoverStart={() => controls.start({ rotate: -10 })}\n        onHoverEnd={() => controls.start({ rotate: 0 })}\n      >\n        <motion.div\n          className=\"text-foreground/60 transition-colors group-hover:text-primary\"\n          animate={controls}\n          transition={{ type: \"spring\", stiffness: 300, damping: 10 }}\n        >\n          {icon || <Icons.logo className=\"h-4 w-4\" />}\n        </motion.div>\n        <span>{text}</span>\n        {endIcon || <Icons.chevronRight className=\"h-4 w-4\" />}\n      </motion.div>\n    </Link>\n  );\n}\n\ninterface HeroContentProps {\n  title: string;\n  titleHighlight?: string;\n  description: string;\n  primaryAction?: {\n    href: string;\n    text: string;\n    icon?: React.ReactNode;\n  };\n  secondaryAction?: {\n    href: string;\n    text: string;\n    icon?: React.ReactNode;\n  };\n}\n\nfunction HeroContent({\n  title,\n  titleHighlight,\n  description,\n  primaryAction,\n  secondaryAction,\n}: HeroContentProps) {\n  return (\n    <div className=\"flex flex-col space-y-4\">\n      <motion.h1\n        className=\"text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl xl:text-8xl\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.8, ease }}\n      >\n        {title}{\" \"}\n        {titleHighlight && (\n          <span className=\"text-primary\">{titleHighlight}</span>\n        )}\n      </motion.h1>\n      <motion.p\n        className=\"max-w-[42rem] leading-normal text-muted-foreground sm:text-xl sm:leading-8\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.1, duration: 0.8, ease }}\n      >\n        {description}\n      </motion.p>\n      <motion.div\n        className=\"flex flex-col sm:flex-row gap-4 pt-4\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.2, duration: 0.8, ease }}\n      >\n        {primaryAction && (\n          <Link\n            href={primaryAction.href}\n            className={cn(\n              buttonVariants({ size: \"lg\" }),\n              \"gap-2 w-full sm:w-auto justify-center\"\n            )}\n          >\n            {primaryAction.icon}\n            {primaryAction.text}\n          </Link>\n        )}\n        {secondaryAction && (\n          <Link\n            href={secondaryAction.href}\n            className={cn(\n              buttonVariants({ variant: \"outline\", size: \"lg\" }),\n              \"gap-2 w-full sm:w-auto justify-center\"\n            )}\n          >\n            {secondaryAction.icon}\n            {secondaryAction.text}\n          </Link>\n        )}\n      </motion.div>\n    </div>\n  );\n}\n\ninterface HeroProps {\n  pill?: {\n    href?: string;\n    text: string;\n    icon?: React.ReactNode;\n    endIcon?: React.ReactNode;\n  };\n  content: HeroContentProps;\n  preview?: React.ReactNode;\n}\n\nexport default function Hero({ pill, content, preview }: HeroProps) {\n  return (\n    <div className=\"container relative overflow-hidden\">\n      <div className=\"flex min-h-[calc(100vh-64px)] flex-col lg:flex-row items-center py-8 px-4 md:px-8 lg:px-12\">\n        <div className=\"flex flex-col gap-4 w-full lg:max-w-2xl\">\n          {pill && <HeroPill {...pill} />}\n          <HeroContent {...content} />\n        </div>\n        {preview && (\n          <div className=\"w-full lg:max-w-xl lg:pl-16 mt-12 lg:mt-0\">\n            {preview}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"}], "dependencies": ["framer-motion"]}