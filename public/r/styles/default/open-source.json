{"name": "open-source", "type": "registry:ui", "code": "\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Separator } from \"@/components/ui/separator\";\nimport Image from \"next/image\";\nimport { cn } from \"@/lib/utils\";\nimport { Suspense } from \"react\";\n\ninterface Contributor {\n  avatar_url: string;\n  login: string;\n}\n\ninterface Stats {\n  stars: number;\n  contributors: Contributor[];\n}\n\ninterface OpenSourceProps {\n  /** The repository owner/name (e.g., \"codehagen/prismui\") */\n  repository: string;\n  /** Optional GitHub OAuth token for API requests */\n  githubToken?: string;\n  /** Optional default stats to show while loading */\n  defaultStats?: Stats;\n  /** Optional custom title */\n  title?: string;\n  /** Optional custom description */\n  description?: string;\n  /** Optional custom button text */\n  buttonText?: string;\n  /** Optional className for styling */\n  className?: string;\n}\n\nasync function getGithubStats(repository: string, githubToken?: string): Promise<Stats> {\n  try {\n    const [repoResponse, contributorsResponse] = await Promise.all([\n      fetch(`https://api.github.com/repos/${repository}`, {\n        ...(githubToken && {\n          headers: {\n            Authorization: `Bearer ${githubToken}`,\n            \"Content-Type\": \"application/json\",\n          },\n        }),\n        next: { revalidate: 3600 },\n      }),\n      fetch(`https://api.github.com/repos/${repository}/contributors`, {\n        ...(githubToken && {\n          headers: {\n            Authorization: `Bearer ${githubToken}`,\n            \"Content-Type\": \"application/json\",\n          },\n        }),\n        next: { revalidate: 3600 },\n      }),\n    ]);\n\n    if (!repoResponse.ok || !contributorsResponse.ok) {\n      return { stars: 0, contributors: [] };\n    }\n\n    const repoData = await repoResponse.json();\n    const contributorsData = await contributorsResponse.json();\n\n    return {\n      stars: repoData.stargazers_count,\n      contributors: contributorsData as Contributor[],\n    };\n  } catch (error) {\n    return { stars: 0, contributors: [] };\n  }\n}\n\nfunction StarIcon({\n  className,\n  delay = 0,\n  size = \"default\",\n}: {\n  className?: string;\n  delay?: number;\n  size?: \"small\" | \"default\";\n}) {\n  return (\n    <motion.div\n      initial={{ opacity: 0, scale: 0 }}\n      animate={{ opacity: 1, scale: 1 }}\n      whileHover={{ scale: 1.2, rotate: 20 }}\n      transition={{\n        duration: 0.8,\n        delay,\n        ease: [0.16, 1, 0.3, 1],\n        hover: {\n          duration: 0.2,\n          ease: \"easeOut\",\n        },\n      }}\n      className={className}\n    >\n      <svg\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n        className={cn(\n          \"text-yellow-400\",\n          size === \"small\" ? \"w-4 h-4\" : \"w-8 h-8\"\n        )}\n      >\n        <path\n          d=\"M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z\"\n          fill=\"currentColor\"\n          stroke=\"currentColor\"\n          strokeWidth=\"2\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n          className={cn(size === \"small\" && \"opacity-20\")}\n        />\n      </svg>\n    </motion.div>\n  );\n}\n\nfunction StarsDecoration() {\n  return (\n    <div className=\"absolute -top-8 left-1/2 -translate-x-1/2\">\n      <div className=\"flex gap-4\">\n        <StarIcon delay={0.2} />\n        <StarIcon delay={0.3} />\n        <StarIcon delay={0.4} />\n      </div>\n    </div>\n  );\n}\n\nfunction ContributorAvatars({ contributors }: { contributors: Contributor[] }) {\n  const displayedContributors = contributors.slice(0, 8);\n\n  return (\n    <div className=\"flex flex-wrap gap-2\">\n      {displayedContributors.map((contributor) => (\n        <motion.div\n          key={contributor.login}\n          whileHover={{ scale: 1.1, y: -3 }}\n          transition={{ type: \"spring\", stiffness: 400, damping: 17 }}\n        >\n          <Image\n            src={contributor.avatar_url}\n            alt={`${contributor.login}'s avatar`}\n            width={40}\n            height={40}\n            className=\"rounded-full border-2 border-background\"\n          />\n        </motion.div>\n      ))}\n    </div>\n  );\n}\n\nfunction OpenSourceCard({\n  repository,\n  stars,\n  contributors,\n}: {\n  repository: string;\n  stars: number;\n  contributors: Contributor[];\n}) {\n  return (\n    <div className=\"relative grid md:grid-cols-2 gap-8 items-center\">\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        whileInView={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.8, ease: [0.16, 1, 0.3, 1] }}\n        viewport={{ once: true }}\n        className=\"relative flex flex-col items-center text-center\"\n      >\n        <motion.a\n          href={`https://github.com/${repository}`}\n          target=\"_blank\"\n          rel=\"noreferrer\"\n          className=\"relative inline-flex flex-col items-center cursor-pointer\"\n          whileHover={{ scale: 1.05 }}\n          transition={{ duration: 0.2 }}\n        >\n          <StarsDecoration />\n          <div className=\"flex flex-col items-center mt-2\">\n            <div className=\"text-7xl font-bold\">{stars}</div>\n            <div className=\"text-xl text-muted-foreground mt-2\">\n              Github Stars\n            </div>\n          </div>\n        </motion.a>\n      </motion.div>\n\n      <Separator className=\"md:hidden\" />\n\n      <div className=\"hidden md:block absolute left-1/2 top-0 h-full\">\n        <Separator orientation=\"vertical\" />\n      </div>\n\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        whileInView={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.8, ease: [0.16, 1, 0.3, 1] }}\n        viewport={{ once: true }}\n        className=\"text-center\"\n      >\n        <div className=\"space-y-4\">\n          <div>\n            <div className=\"text-3xl font-bold\">\n              {contributors.length}+ Contributors\n            </div>\n            <div className=\"text-lg text-muted-foreground mt-2\">\n              Join our growing community\n            </div>\n          </div>\n          <a\n            href={`https://github.com/${repository}/graphs/contributors`}\n            target=\"_blank\"\n            rel=\"noreferrer\"\n            className=\"inline-block\"\n          >\n            <div className=\"flex justify-center\">\n              <ContributorAvatars contributors={contributors} />\n            </div>\n          </a>\n        </div>\n      </motion.div>\n    </div>\n  );\n}\n\nfunction OpenSourceContent({\n  repository,\n  stars,\n  contributors,\n  title = \"Proudly open-source\",\n  description = \"Our source code is available on GitHub - feel free to read, review, or contribute to it however you want!\",\n  buttonText = \"Star on GitHub\",\n}: Stats & {\n  repository: string;\n  title?: string;\n  description?: string;\n  buttonText?: string;\n}) {\n  return (\n    <section className=\"container relative py-20\">\n      <div className=\"text-center mb-16\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, ease: [0.16, 1, 0.3, 1] }}\n          viewport={{ once: true }}\n        >\n          <h2 className=\"text-4xl font-bold tracking-tight sm:text-6xl mb-4\">\n            {title}\n          </h2>\n          <p className=\"text-xl text-muted-foreground max-w-[800px] mx-auto\">\n            {description}\n          </p>\n          <div className=\"mt-6\">\n            <Button variant=\"outline\" size=\"lg\" className=\"gap-2\" asChild>\n              <a\n                href={`https://github.com/${repository}`}\n                target=\"_blank\"\n                rel=\"noreferrer\"\n              >\n                <svg viewBox=\"0 0 438.549 438.549\" className=\"h-5 w-5\">\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M409.132 114.573c-19.608-33.596-46.205-60.194-79.798-79.8-33.598-19.607-70.277-29.408-110.063-29.408-39.781 0-76.472 9.804-110.063 29.408-33.596 19.605-60.192 46.204-79.8 79.8C9.803 148.168 0 184.854 0 224.63c0 47.78 13.94 90.745 41.827 128.906 27.884 38.164 63.906 64.572 108.063 79.227 5.14.954 8.945.283 11.419-1.996 2.475-2.282 3.711-5.14 3.711-8.562 0-.571-.049-5.708-.144-15.417a2549.81 2549.81 0 01-.144-25.406l-6.567 1.136c-4.187.767-9.469 1.092-15.846 1-6.374-.089-12.991-.757-19.842-1.999-6.854-1.231-13.229-4.086-19.13-8.559-5.898-4.473-10.085-10.328-12.56-17.556l-2.855-6.57c-1.903-4.374-4.899-9.233-8.992-14.559-4.093-5.331-8.232-8.945-12.419-10.848l-1.999-1.431c-1.332-.951-2.568-2.098-3.711-3.429-1.142-1.331-1.997-2.663-2.568-3.997-.572-1.335-.098-2.43 1.427-3.289 1.525-.859 4.281-1.276 8.28-1.276l5.708.853c3.807.763 8.516 3.042 14.133 6.851 5.614 3.806 10.229 8.754 13.846 14.842 4.38 7.806 9.657 13.754 15.846 17.847 6.184 4.093 12.419 6.136 18.699 6.136 6.28 0 11.704-.476 16.274-1.423 4.565-.952 8.848-2.383 12.847-4.285 1.713-12.758 6.377-22.559 13.988-29.41-10.848-1.14-20.601-2.857-29.264-5.14-8.658-2.286-17.605-5.996-26.835-11.14-9.235-5.137-16.896-11.516-22.985-19.126-6.09-7.614-11.088-17.61-14.987-29.979-3.901-12.374-5.852-26.648-5.852-42.826 0-23.035 7.52-42.637 22.557-58.817-7.044-17.318-6.379-36.732 1.997-58.24 5.52-1.715 13.706-.428 24.554 3.853 10.85 4.283 18.794 7.952 23.84 10.994 5.046 3.041 9.089 5.618 12.135 7.708 17.705-4.947 35.976-7.421 54.818-7.421s37.117 2.474 54.823 7.421l10.849-6.849c7.419-4.57 16.18-8.758 26.262-12.565 10.088-3.805 17.802-4.853 23.134-3.138 8.562 21.509 9.325 40.922 2.279 58.24 15.036 16.18 22.559 35.787 22.559 58.817 0 16.178-1.958 30.497-5.853 42.966-3.9 12.471-8.941 22.457-15.125 29.979-6.191 7.521-13.901 13.85-23.131 18.986-9.232 5.14-18.182 8.85-26.84 11.136-8.662 2.286-18.415 4.004-29.263 5.146 9.894 8.562 14.842 22.077 14.842 40.539v60.237c0 3.422 1.19 6.279 3.572 8.562 2.379 2.279 6.136 2.95 11.276 1.995 44.163-14.653 80.185-41.062 108.068-79.226 27.88-38.161 41.825-81.126 41.825-128.906-.01-39.771-9.818-76.454-29.414-110.049z\"\n                  ></path>\n                </svg>\n                {buttonText}\n              </a>\n            </Button>\n          </div>\n        </motion.div>\n      </div>\n      <Separator className=\"mb-16\" />\n      <div className=\"max-w-4xl mx-auto\">\n        <OpenSourceCard\n          repository={repository}\n          stars={stars}\n          contributors={contributors}\n        />\n      </div>\n    </section>\n  );\n}\n\nasync function OpenSourceData({\n  repository,\n  githubToken,\n  defaultStats = { stars: 0, contributors: [] },\n  ...props\n}: OpenSourceProps) {\n  const stats = await getGithubStats(repository, githubToken);\n  return <OpenSourceContent {...stats} {...props} />;\n}\n\nexport default function OpenSource(props: OpenSourceProps) {\n  return (\n    <Suspense\n      fallback={\n        <OpenSourceContent\n          stars={props.defaultStats?.stars || 0}\n          contributors={props.defaultStats?.contributors || []}\n          {...props}\n        />\n      }\n    >\n      <OpenSourceData {...props} />\n    </Suspense>\n  );\n}", "files": [{"path": "components/prismui/open-source.tsx", "type": "registry:ui", "content": "\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Separator } from \"@/components/ui/separator\";\nimport Image from \"next/image\";\nimport { cn } from \"@/lib/utils\";\nimport { Suspense } from \"react\";\n\ninterface Contributor {\n  avatar_url: string;\n  login: string;\n}\n\ninterface Stats {\n  stars: number;\n  contributors: Contributor[];\n}\n\ninterface OpenSourceProps {\n  /** The repository owner/name (e.g., \"codehagen/prismui\") */\n  repository: string;\n  /** Optional GitHub OAuth token for API requests */\n  githubToken?: string;\n  /** Optional default stats to show while loading */\n  defaultStats?: Stats;\n  /** Optional custom title */\n  title?: string;\n  /** Optional custom description */\n  description?: string;\n  /** Optional custom button text */\n  buttonText?: string;\n  /** Optional className for styling */\n  className?: string;\n}\n\n/**\n * Example of how to fetch GitHub stats in your server component or action:\n *\n * ```ts\n * async function getGithubStats(repository: string, githubToken?: string): Promise<Stats> {\n *   try {\n *     const [repoResponse, contributorsResponse] = await Promise.all([\n *       fetch(`https://api.github.com/repos/${repository}`, {\n *         ...(githubToken && {\n *           headers: {\n *             Authorization: `Bearer ${githubToken}`,\n *             \"Content-Type\": \"application/json\",\n *           },\n *         }),\n *         next: { revalidate: 3600 },\n *       }),\n *       fetch(`https://api.github.com/repos/${repository}/contributors`, {\n *         ...(githubToken && {\n *           headers: {\n *             Authorization: `Bearer ${githubToken}`,\n *             \"Content-Type\": \"application/json\",\n *           },\n *         }),\n *         next: { revalidate: 3600 },\n *       }),\n *     ]);\n *\n *     if (!repoResponse.ok || !contributorsResponse.ok) {\n *       return { stars: 0, contributors: [] };\n *     }\n *\n *     const repoData = await repoResponse.json();\n *     const contributorsData = await contributorsResponse.json();\n *\n *     return {\n *       stars: repoData.stargazers_count,\n *       contributors: contributorsData as Contributor[],\n *     };\n *   } catch (error) {\n *     return { stars: 0, contributors: [] };\n *   }\n * }\n * ```\n */\n\nfunction StarIcon({\n  className,\n  delay = 0,\n  size = \"default\",\n}: {\n  className?: string;\n  delay?: number;\n  size?: \"small\" | \"default\";\n}) {\n  return (\n    <motion.div\n      initial={{ opacity: 0, scale: 0 }}\n      animate={{ opacity: 1, scale: 1 }}\n      whileHover={{ scale: 1.2, rotate: 20 }}\n      transition={{\n        duration: 0.8,\n        delay,\n        ease: [0.16, 1, 0.3, 1],\n        hover: {\n          duration: 0.2,\n          ease: \"easeOut\",\n        },\n      }}\n      className={className}\n    >\n      <svg\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n        className={cn(\n          \"text-yellow-400\",\n          size === \"small\" ? \"w-4 h-4\" : \"w-8 h-8\"\n        )}\n      >\n        <path\n          d=\"M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z\"\n          fill=\"currentColor\"\n          stroke=\"currentColor\"\n          strokeWidth=\"2\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n          className={cn(size === \"small\" && \"opacity-20\")}\n        />\n      </svg>\n    </motion.div>\n  );\n}\n\nfunction StarsDecoration() {\n  return (\n    <div className=\"absolute -top-8 left-1/2 -translate-x-1/2\">\n      <div className=\"flex gap-4\">\n        <StarIcon delay={0.2} />\n        <StarIcon delay={0.3} />\n        <StarIcon delay={0.4} />\n      </div>\n    </div>\n  );\n}\n\nfunction ContributorAvatars({ contributors }: { contributors: Contributor[] }) {\n  const displayedContributors = contributors.slice(0, 8);\n\n  return (\n    <div className=\"flex flex-wrap gap-2\">\n      {displayedContributors.map((contributor) => (\n        <motion.div\n          key={contributor.login}\n          whileHover={{ scale: 1.1, y: -3 }}\n          transition={{ type: \"spring\", stiffness: 400, damping: 17 }}\n        >\n          <Image\n            src={contributor.avatar_url}\n            alt={`${contributor.login}'s avatar`}\n            width={40}\n            height={40}\n            className=\"rounded-full border-2 border-background\"\n          />\n        </motion.div>\n      ))}\n    </div>\n  );\n}\n\nfunction OpenSourceCard({\n  repository,\n  stars,\n  contributors,\n}: {\n  repository: string;\n  stars: number;\n  contributors: Contributor[];\n}) {\n  return (\n    <div className=\"relative grid md:grid-cols-2 gap-8 items-center\">\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        whileInView={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.8, ease: [0.16, 1, 0.3, 1] }}\n        viewport={{ once: true }}\n        className=\"relative flex flex-col items-center text-center\"\n      >\n        <motion.a\n          href={`https://github.com/${repository}`}\n          target=\"_blank\"\n          rel=\"noreferrer\"\n          className=\"relative inline-flex flex-col items-center cursor-pointer\"\n          whileHover={{ scale: 1.05 }}\n          transition={{ duration: 0.2 }}\n        >\n          <StarsDecoration />\n          <div className=\"flex flex-col items-center mt-2\">\n            <div className=\"text-7xl font-bold\">{stars}</div>\n            <div className=\"text-xl text-muted-foreground mt-2\">\n              Github Stars\n            </div>\n          </div>\n        </motion.a>\n      </motion.div>\n\n      <Separator className=\"md:hidden\" />\n\n      <div className=\"hidden md:block absolute left-1/2 top-0 h-full\">\n        <Separator orientation=\"vertical\" />\n      </div>\n\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        whileInView={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.8, ease: [0.16, 1, 0.3, 1] }}\n        viewport={{ once: true }}\n        className=\"text-center\"\n      >\n        <div className=\"space-y-4\">\n          <div>\n            <div className=\"text-3xl font-bold\">\n              {contributors.length}+ Contributors\n            </div>\n            <div className=\"text-lg text-muted-foreground mt-2\">\n              Join our growing community\n            </div>\n          </div>\n          <a\n            href={`https://github.com/${repository}/graphs/contributors`}\n            target=\"_blank\"\n            rel=\"noreferrer\"\n            className=\"inline-block\"\n          >\n            <div className=\"flex justify-center\">\n              <ContributorAvatars contributors={contributors} />\n            </div>\n          </a>\n        </div>\n      </motion.div>\n    </div>\n  );\n}\n\nfunction OpenSourceContent({\n  repository,\n  stars,\n  contributors,\n  title = \"Proudly open-source\",\n  description = \"Our source code is available on GitHub - feel free to read, review, or contribute to it however you want!\",\n  buttonText = \"Star on GitHub\",\n}: Stats & {\n  repository: string;\n  title?: string;\n  description?: string;\n  buttonText?: string;\n}) {\n  return (\n    <section className=\"container relative py-20\">\n      <div className=\"text-center mb-16\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, ease: [0.16, 1, 0.3, 1] }}\n          viewport={{ once: true }}\n        >\n          <h2 className=\"text-4xl font-bold tracking-tight sm:text-6xl mb-4\">\n            {title}\n          </h2>\n          <p className=\"text-xl text-muted-foreground max-w-[800px] mx-auto\">\n            {description}\n          </p>\n          <div className=\"mt-6\">\n            <Button variant=\"outline\" size=\"lg\" className=\"gap-2\" asChild>\n              <a\n                href={`https://github.com/${repository}`}\n                target=\"_blank\"\n                rel=\"noreferrer\"\n              >\n                <svg viewBox=\"0 0 438.549 438.549\" className=\"h-5 w-5\">\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M409.132 114.573c-19.608-33.596-46.205-60.194-79.798-79.8-33.598-19.607-70.277-29.408-110.063-29.408-39.781 0-76.472 9.804-110.063 29.408-33.596 19.605-60.192 46.204-79.8 79.8C9.803 148.168 0 184.854 0 224.63c0 47.78 13.94 90.745 41.827 128.906 27.884 38.164 63.906 64.572 108.063 79.227 5.14.954 8.945.283 11.419-1.996 2.475-2.282 3.711-5.14 3.711-8.562 0-.571-.049-5.708-.144-15.417a2549.81 2549.81 0 01-.144-25.406l-6.567 1.136c-4.187.767-9.469 1.092-15.846 1-6.374-.089-12.991-.757-19.842-1.999-6.854-1.231-13.229-4.086-19.13-8.559-5.898-4.473-10.085-10.328-12.56-17.556l-2.855-6.57c-1.903-4.374-4.899-9.233-8.992-14.559-4.093-5.331-8.232-8.945-12.419-10.848l-1.999-1.431c-1.332-.951-2.568-2.098-3.711-3.429-1.142-1.331-1.997-2.663-2.568-3.997-.572-1.335-.098-2.43 1.427-3.289 1.525-.859 4.281-1.276 8.28-1.276l5.708.853c3.807.763 8.516 3.042 14.133 6.851 5.614 3.806 10.229 8.754 13.846 14.842 4.38 7.806 9.657 13.754 15.846 17.847 6.184 4.093 12.419 6.136 18.699 6.136 6.28 0 11.704-.476 16.274-1.423 4.565-.952 8.848-2.383 12.847-4.285 1.713-12.758 6.377-22.559 13.988-29.41-10.848-1.14-20.601-2.857-29.264-5.14-8.658-2.286-17.605-5.996-26.835-11.14-9.235-5.137-16.896-11.516-22.985-19.126-6.09-7.614-11.088-17.61-14.987-29.979-3.901-12.374-5.852-26.648-5.852-42.826 0-23.035 7.52-42.637 22.557-58.817-7.044-17.318-6.379-36.732 1.997-58.24 5.52-1.715 13.706-.428 24.554 3.853 10.85 4.283 18.794 7.952 23.84 10.994 5.046 3.041 9.089 5.618 12.135 7.708 17.705-4.947 35.976-7.421 54.818-7.421s37.117 2.474 54.823 7.421l10.849-6.849c7.419-4.57 16.18-8.758 26.262-12.565 10.088-3.805 17.802-4.853 23.134-3.138 8.562 21.509 9.325 40.922 2.279 58.24 15.036 16.18 22.559 35.787 22.559 58.817 0 16.178-1.958 30.497-5.853 42.966-3.9 12.471-8.941 22.457-15.125 29.979-6.191 7.521-13.901 13.85-23.131 18.986-9.232 5.14-18.182 8.85-26.84 11.136-8.662 2.286-18.415 4.004-29.263 5.146 9.894 8.562 14.842 22.077 14.842 40.539v60.237c0 3.422 1.19 6.279 3.572 8.562 2.379 2.279 6.136 2.95 11.276 1.995 44.163-14.653 80.185-41.062 108.068-79.226 27.88-38.161 41.825-81.126 41.825-128.906-.01-39.771-9.818-76.454-29.414-110.049z\"\n                  ></path>\n                </svg>\n                {buttonText}\n              </a>\n            </Button>\n          </div>\n        </motion.div>\n      </div>\n      <Separator className=\"mb-16\" />\n      <div className=\"max-w-4xl mx-auto\">\n        <OpenSourceCard\n          repository={repository}\n          stars={stars}\n          contributors={contributors}\n        />\n      </div>\n    </section>\n  );\n}\n\nexport default function OpenSource({\n  repository,\n  defaultStats = { stars: 0, contributors: [] },\n  ...props\n}: OpenSourceProps) {\n  // This is a placeholder component. You should implement the data fetching\n  // in your server component using the example code above.\n  return (\n    <OpenSourceContent\n      repository={repository}\n      stars={defaultStats.stars}\n      contributors={defaultStats.contributors}\n      {...props}\n    />\n  );\n}\n"}], "dependencies": ["framer-motion"]}