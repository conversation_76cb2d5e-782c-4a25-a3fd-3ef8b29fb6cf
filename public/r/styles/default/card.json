{"name": "card", "type": "registry:ui", "code": "\"use client\";\n\nimport * as React from \"react\";\nimport { cn } from \"@/lib/utils\";\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  /** Optional hover effect */\n  hover?: boolean;\n  /** Optional gradient background */\n  gradient?: boolean;\n  /** Optional border style */\n  bordered?: boolean;\n}\n\nexport default function Card({\n  className,\n  hover = false,\n  gradient = false,\n  bordered = false,\n  children,\n  ...props\n}: CardProps) {\n  return (\n    <div\n      className={cn(\n        \"rounded-lg bg-card p-6\",\n        {\n          \"transition-all duration-200 hover:scale-[1.02] hover:shadow-lg\": hover,\n          \"bg-gradient-to-br from-card/50 to-card\": gradient,\n          \"border border-border\": bordered,\n        },\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n}\n\ninterface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {}\n\nexport function CardHeader({ className, ...props }: CardHeaderProps) {\n  return <div className={cn(\"mb-4\", className)} {...props} />;\n}\n\ninterface CardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {}\n\nexport function CardTitle({ className, ...props }: CardTitleProps) {\n  return (\n    <h3\n      className={cn(\"text-2xl font-semibold tracking-tight\", className)}\n      {...props}\n    />\n  );\n}\n\ninterface CardDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {}\n\nexport function CardDescription({ className, ...props }: CardDescriptionProps) {\n  return (\n    <p\n      className={cn(\"text-sm text-muted-foreground\", className)}\n      {...props}\n    />\n  );\n}\n\ninterface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {}\n\nexport function CardContent({ className, ...props }: CardContentProps) {\n  return <div className={cn(\"\", className)} {...props} />;\n}\n\ninterface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {}\n\nexport function CardFooter({ className, ...props }: CardFooterProps) {\n  return (\n    <div\n      className={cn(\"mt-4 flex items-center justify-between\", className)}\n      {...props}\n    />\n  );\n}", "files": [{"path": "components/prismui/card.tsx", "type": "registry:ui", "content": "\"use client\";\n\nimport * as React from \"react\";\nimport { cn } from \"@/lib/utils\";\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  /** Optional hover effect */\n  hover?: boolean;\n  /** Optional gradient background */\n  gradient?: boolean;\n  /** Optional border style */\n  bordered?: boolean;\n}\n\nexport default function Card({\n  className,\n  hover = false,\n  gradient = false,\n  bordered = false,\n  children,\n  ...props\n}: CardProps) {\n  return (\n    <div\n      className={cn(\n        \"rounded-lg bg-card p-6\",\n        {\n          \"transition-all duration-200 hover:scale-[1.02] hover:shadow-lg\":\n            hover,\n          \"bg-gradient-to-br from-card/50 to-card\": gradient,\n          \"border border-border\": bordered,\n        },\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n}\n\ninterface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {}\n\nexport function CardHeader({ className, ...props }: CardHeaderProps) {\n  return <div className={cn(\"mb-4\", className)} {...props} />;\n}\n\ninterface CardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {}\n\nexport function CardTitle({ className, ...props }: CardTitleProps) {\n  return (\n    <h3\n      className={cn(\"text-2xl font-semibold tracking-tight\", className)}\n      {...props}\n    />\n  );\n}\n\ninterface CardDescriptionProps\n  extends React.HTMLAttributes<HTMLParagraphElement> {}\n\nexport function CardDescription({ className, ...props }: CardDescriptionProps) {\n  return (\n    <p className={cn(\"text-sm text-muted-foreground\", className)} {...props} />\n  );\n}\n\ninterface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {}\n\nexport function CardContent({ className, ...props }: CardContentProps) {\n  return <div className={cn(\"\", className)} {...props} />;\n}\n\ninterface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {}\n\nexport function CardFooter({ className, ...props }: CardFooterProps) {\n  return (\n    <div\n      className={cn(\"mt-4 flex items-center justify-between\", className)}\n      {...props}\n    />\n  );\n}\n"}], "dependencies": []}