{"name": "timeline", "type": "registry:ui", "code": "\"use client\";\n\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport Link from \"next/link\";\nimport { ChevronDown } from \"lucide-react\";\nimport { useState } from \"react\";\nimport { cn } from \"@/lib/utils\";\n\ninterface TimelineItem {\n  date: string;\n  title: string;\n  description?: string;\n  href?: string;\n  icon?: React.ReactNode;\n}\n\ninterface TimelineProps {\n  items: TimelineItem[];\n  initialCount?: number;\n  dateFormat?: Intl.DateTimeFormatOptions;\n  className?: string;\n  showMoreText?: string;\n  showLessText?: string;\n  dotClassName?: string;\n  lineClassName?: string;\n  titleClassName?: string;\n  descriptionClassName?: string;\n  dateClassName?: string;\n  buttonVariant?: \"default\" | \"outline\" | \"ghost\" | \"link\";\n  buttonSize?: \"default\" | \"sm\" | \"lg\";\n  animationDuration?: number;\n  animationDelay?: number;\n  showAnimation?: boolean;\n}\n\nfunction DesktopTimelineEntry({\n  item,\n  dotClassName,\n  lineClassName,\n  titleClassName,\n  descriptionClassName,\n  dateClassName,\n}: {\n  item: TimelineItem;\n  dotClassName?: string;\n  lineClassName?: string;\n  titleClassName?: string;\n  descriptionClassName?: string;\n  dateClassName?: string;\n}) {\n  return (\n    <Link\n      href={item.href || \"#\"}\n      className={cn(\n        \"group hidden grid-cols-9 items-center md:grid\",\n        !item.href && \"pointer-events-none\"\n      )}\n    >\n      <dl className=\"col-span-2\">\n        <dt className=\"sr-only\">Date</dt>\n        <dd\n          className={cn(\n            \"text-base font-medium text-muted-foreground transition-colors group-hover:text-foreground\",\n            dateClassName\n          )}\n        >\n          <time dateTime={item.date}>\n            {new Date(item.date).toLocaleDateString(\"en-US\", {\n              month: \"long\",\n              day: \"numeric\",\n              year: \"numeric\",\n            })}\n          </time>\n        </dd>\n      </dl>\n      <div className=\"col-span-7 flex items-center\">\n        <div className=\"relative ml-4\">\n          <div\n            className={cn(\"h-16 border-l border-border pr-8\", lineClassName)}\n          />\n          <div\n            className={cn(\n              \"absolute -left-1 top-[1.6875rem] flex h-5 w-5 items-center justify-center rounded-full bg-primary/60 transition-colors group-hover:bg-primary\",\n              !item.icon && \"h-2.5 w-2.5\",\n              dotClassName\n            )}\n          >\n            {item.icon && (\n              <div className=\"h-3 w-3 text-primary-foreground\">{item.icon}</div>\n            )}\n          </div>\n        </div>\n        <div className=\"flex flex-col gap-1\">\n          <h3\n            className={cn(\n              \"text-xl font-medium tracking-tight text-muted-foreground transition-colors group-hover:text-foreground\",\n              titleClassName\n            )}\n          >\n            {item.title}\n          </h3>\n          {item.description && (\n            <p\n              className={cn(\n                \"text-sm text-muted-foreground group-hover:text-muted-foreground/80\",\n                descriptionClassName\n              )}\n            >\n              {item.description}\n            </p>\n          )}\n        </div>\n      </div>\n    </Link>\n  );\n}\n\nfunction MobileTimelineEntry({\n  item,\n  dotClassName,\n  lineClassName,\n  titleClassName,\n  descriptionClassName,\n  dateClassName,\n}: {\n  item: TimelineItem;\n  dotClassName?: string;\n  lineClassName?: string;\n  titleClassName?: string;\n  descriptionClassName?: string;\n  dateClassName?: string;\n}) {\n  return (\n    <Link\n      href={item.href || \"#\"}\n      className={cn(\n        \"flex items-center space-x-4 rounded-lg px-4 py-3 transition-colors hover:bg-muted active:bg-muted/80 md:hidden\",\n        !item.href && \"pointer-events-none\"\n      )}\n    >\n      <div className=\"relative\">\n        <div className={cn(\"h-16 border-l border-border\", lineClassName)} />\n        <div\n          className={cn(\n            \"absolute -left-1 top-5 flex h-5 w-5 items-center justify-center rounded-full bg-primary/60\",\n            !item.icon && \"h-2.5 w-2.5\",\n            dotClassName\n          )}\n        >\n          {item.icon && (\n            <div className=\"h-3 w-3 text-primary-foreground\">{item.icon}</div>\n          )}\n        </div>\n      </div>\n      <div>\n        <dl>\n          <dt className=\"sr-only\">Date</dt>\n          <dd\n            className={cn(\n              \"text-sm font-medium text-muted-foreground\",\n              dateClassName\n            )}\n          >\n            <time dateTime={item.date}>\n              {new Date(item.date).toLocaleDateString(\"en-US\", {\n                month: \"long\",\n                day: \"numeric\",\n                year: \"numeric\",\n              })}\n            </time>\n          </dd>\n        </dl>\n        <h3\n          className={cn(\n            \"text-lg font-medium tracking-tight text-foreground\",\n            titleClassName\n          )}\n        >\n          {item.title}\n        </h3>\n        {item.description && (\n          <p\n            className={cn(\n              \"text-sm text-muted-foreground\",\n              descriptionClassName\n            )}\n          >\n            {item.description}\n          </p>\n        )}\n      </div>\n    </Link>\n  );\n}\n\nexport function Timeline({\n  items,\n  initialCount = 5,\n  className,\n  showMoreText = \"Show More\",\n  showLessText = \"Show Less\",\n  dotClassName,\n  lineClassName,\n  titleClassName,\n  descriptionClassName,\n  dateClassName,\n  buttonVariant = \"ghost\",\n  buttonSize = \"sm\",\n  animationDuration = 0.3,\n  animationDelay = 0.1,\n  showAnimation = true,\n}: TimelineProps) {\n  const [showAll, setShowAll] = useState(false);\n  const sortedItems = items.sort(\n    (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()\n  );\n  const initialItems = sortedItems.slice(0, initialCount);\n  const remainingItems = sortedItems.slice(initialCount);\n\n  return (\n    <div className={cn(\"mx-5 max-w-2xl md:mx-auto\", className)}>\n      <div className=\"md:translate-x-28\">\n        <ul className=\"space-y-8\">\n          {initialItems.map((item, index) => (\n            <motion.li\n              key={index}\n              initial={showAnimation ? { opacity: 0, y: 20 } : false}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{\n                duration: animationDuration,\n                delay: index * animationDelay,\n              }}\n            >\n              <DesktopTimelineEntry\n                item={item}\n                dotClassName={dotClassName}\n                lineClassName={lineClassName}\n                titleClassName={titleClassName}\n                descriptionClassName={descriptionClassName}\n                dateClassName={dateClassName}\n              />\n              <MobileTimelineEntry\n                item={item}\n                dotClassName={dotClassName}\n                lineClassName={lineClassName}\n                titleClassName={titleClassName}\n                descriptionClassName={descriptionClassName}\n                dateClassName={dateClassName}\n              />\n            </motion.li>\n          ))}\n          <AnimatePresence>\n            {showAll &&\n              remainingItems.map((item, index) => (\n                <motion.li\n                  key={index}\n                  initial={{ opacity: 0, height: 0 }}\n                  animate={{ opacity: 1, height: \"auto\" }}\n                  exit={{ opacity: 0, height: 0 }}\n                  transition={{\n                    duration: animationDuration,\n                    delay: index * animationDelay,\n                  }}\n                >\n                  <DesktopTimelineEntry\n                    item={item}\n                    dotClassName={dotClassName}\n                    lineClassName={lineClassName}\n                    titleClassName={titleClassName}\n                    descriptionClassName={descriptionClassName}\n                    dateClassName={dateClassName}\n                  />\n                  <MobileTimelineEntry\n                    item={item}\n                    dotClassName={dotClassName}\n                    lineClassName={lineClassName}\n                    titleClassName={titleClassName}\n                    descriptionClassName={descriptionClassName}\n                    dateClassName={dateClassName}\n                  />\n                </motion.li>\n              ))}\n          </AnimatePresence>\n        </ul>\n      </div>\n      {remainingItems.length > 0 && (\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          className=\"mt-8 flex justify-center\"\n        >\n          <Button\n            variant={buttonVariant}\n            size={buttonSize}\n            className=\"gap-2\"\n            onClick={() => setShowAll(!showAll)}\n          >\n            {showAll ? showLessText : showMoreText}\n            <motion.div\n              animate={{ rotate: showAll ? 180 : 0 }}\n              transition={{ duration: 0.2 }}\n            >\n              <ChevronDown className=\"h-4 w-4\" />\n            </motion.div>\n          </Button>\n        </motion.div>\n      )}\n    </div>\n  );\n}\n", "files": [{"path": "components/prismui/timeline.tsx", "type": "registry:ui", "content": "\"use client\";\n\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport Link from \"next/link\";\nimport { ChevronDown } from \"lucide-react\";\nimport { useState } from \"react\";\nimport { cn } from \"@/lib/utils\";\n\ninterface TimelineItem {\n  date: string;\n  title: string;\n  description?: string;\n  href?: string;\n  icon?: React.ReactNode;\n}\n\ninterface TimelineProps {\n  items: TimelineItem[];\n  initialCount?: number;\n  dateFormat?: Intl.DateTimeFormatOptions;\n  className?: string;\n  showMoreText?: string;\n  showLessText?: string;\n  dotClassName?: string;\n  lineClassName?: string;\n  titleClassName?: string;\n  descriptionClassName?: string;\n  dateClassName?: string;\n  buttonVariant?: \"default\" | \"outline\" | \"ghost\" | \"link\";\n  buttonSize?: \"default\" | \"sm\" | \"lg\";\n  animationDuration?: number;\n  animationDelay?: number;\n  showAnimation?: boolean;\n}\n\nfunction DesktopTimelineEntry({\n  item,\n  dotClassName,\n  lineClassName,\n  titleClassName,\n  descriptionClassName,\n  dateClassName,\n}: {\n  item: TimelineItem;\n  dotClassName?: string;\n  lineClassName?: string;\n  titleClassName?: string;\n  descriptionClassName?: string;\n  dateClassName?: string;\n}) {\n  return (\n    <Link\n      href={item.href || \"#\"}\n      className={cn(\n        \"group hidden grid-cols-9 items-center md:grid\",\n        !item.href && \"pointer-events-none\"\n      )}\n    >\n      <dl className=\"col-span-2\">\n        <dt className=\"sr-only\">Date</dt>\n        <dd\n          className={cn(\n            \"text-base font-medium text-muted-foreground transition-colors group-hover:text-foreground\",\n            dateClassName\n          )}\n        >\n          <time dateTime={item.date}>\n            {new Date(item.date).toLocaleDateString(\"en-US\", {\n              month: \"long\",\n              day: \"numeric\",\n              year: \"numeric\",\n            })}\n          </time>\n        </dd>\n      </dl>\n      <div className=\"col-span-7 flex items-center\">\n        <div className=\"relative ml-4\">\n          <div\n            className={cn(\"h-16 border-l border-border pr-8\", lineClassName)}\n          />\n          <div\n            className={cn(\n              \"absolute -left-1 top-[1.6875rem] flex h-5 w-5 items-center justify-center rounded-full bg-primary/60 transition-colors group-hover:bg-primary\",\n              !item.icon && \"h-2.5 w-2.5\",\n              dotClassName\n            )}\n          >\n            {item.icon && (\n              <div className=\"h-3 w-3 text-primary-foreground\">{item.icon}</div>\n            )}\n          </div>\n        </div>\n        <div className=\"flex flex-col gap-1\">\n          <h3\n            className={cn(\n              \"text-xl font-medium tracking-tight text-muted-foreground transition-colors group-hover:text-foreground\",\n              titleClassName\n            )}\n          >\n            {item.title}\n          </h3>\n          {item.description && (\n            <p\n              className={cn(\n                \"text-sm text-muted-foreground group-hover:text-muted-foreground/80\",\n                descriptionClassName\n              )}\n            >\n              {item.description}\n            </p>\n          )}\n        </div>\n      </div>\n    </Link>\n  );\n}\n\nfunction MobileTimelineEntry({\n  item,\n  dotClassName,\n  lineClassName,\n  titleClassName,\n  descriptionClassName,\n  dateClassName,\n}: {\n  item: TimelineItem;\n  dotClassName?: string;\n  lineClassName?: string;\n  titleClassName?: string;\n  descriptionClassName?: string;\n  dateClassName?: string;\n}) {\n  return (\n    <Link\n      href={item.href || \"#\"}\n      className={cn(\n        \"flex items-center space-x-4 rounded-lg px-4 py-3 transition-colors hover:bg-muted active:bg-muted/80 md:hidden\",\n        !item.href && \"pointer-events-none\"\n      )}\n    >\n      <div className=\"relative\">\n        <div className={cn(\"h-16 border-l border-border\", lineClassName)} />\n        <div\n          className={cn(\n            \"absolute -left-1 top-5 flex h-5 w-5 items-center justify-center rounded-full bg-primary/60\",\n            !item.icon && \"h-2.5 w-2.5\",\n            dotClassName\n          )}\n        >\n          {item.icon && (\n            <div className=\"h-3 w-3 text-primary-foreground\">{item.icon}</div>\n          )}\n        </div>\n      </div>\n      <div>\n        <dl>\n          <dt className=\"sr-only\">Date</dt>\n          <dd\n            className={cn(\n              \"text-sm font-medium text-muted-foreground\",\n              dateClassName\n            )}\n          >\n            <time dateTime={item.date}>\n              {new Date(item.date).toLocaleDateString(\"en-US\", {\n                month: \"long\",\n                day: \"numeric\",\n                year: \"numeric\",\n              })}\n            </time>\n          </dd>\n        </dl>\n        <h3\n          className={cn(\n            \"text-lg font-medium tracking-tight text-foreground\",\n            titleClassName\n          )}\n        >\n          {item.title}\n        </h3>\n        {item.description && (\n          <p\n            className={cn(\n              \"text-sm text-muted-foreground\",\n              descriptionClassName\n            )}\n          >\n            {item.description}\n          </p>\n        )}\n      </div>\n    </Link>\n  );\n}\n\nexport function Timeline({\n  items,\n  initialCount = 5,\n  className,\n  showMoreText = \"Show More\",\n  showLessText = \"Show Less\",\n  dotClassName,\n  lineClassName,\n  titleClassName,\n  descriptionClassName,\n  dateClassName,\n  buttonVariant = \"ghost\",\n  buttonSize = \"sm\",\n  animationDuration = 0.3,\n  animationDelay = 0.1,\n  showAnimation = true,\n}: TimelineProps) {\n  const [showAll, setShowAll] = useState(false);\n  const sortedItems = items.sort(\n    (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()\n  );\n  const initialItems = sortedItems.slice(0, initialCount);\n  const remainingItems = sortedItems.slice(initialCount);\n\n  return (\n    <div className={cn(\"mx-5 max-w-2xl md:mx-auto\", className)}>\n      <div className=\"md:translate-x-28\">\n        <ul className=\"space-y-8\">\n          {initialItems.map((item, index) => (\n            <motion.li\n              key={index}\n              initial={showAnimation ? { opacity: 0, y: 20 } : false}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{\n                duration: animationDuration,\n                delay: index * animationDelay,\n              }}\n            >\n              <DesktopTimelineEntry\n                item={item}\n                dotClassName={dotClassName}\n                lineClassName={lineClassName}\n                titleClassName={titleClassName}\n                descriptionClassName={descriptionClassName}\n                dateClassName={dateClassName}\n              />\n              <MobileTimelineEntry\n                item={item}\n                dotClassName={dotClassName}\n                lineClassName={lineClassName}\n                titleClassName={titleClassName}\n                descriptionClassName={descriptionClassName}\n                dateClassName={dateClassName}\n              />\n            </motion.li>\n          ))}\n          <AnimatePresence>\n            {showAll &&\n              remainingItems.map((item, index) => (\n                <motion.li\n                  key={index}\n                  initial={{ opacity: 0, height: 0 }}\n                  animate={{ opacity: 1, height: \"auto\" }}\n                  exit={{ opacity: 0, height: 0 }}\n                  transition={{\n                    duration: animationDuration,\n                    delay: index * animationDelay,\n                  }}\n                >\n                  <DesktopTimelineEntry\n                    item={item}\n                    dotClassName={dotClassName}\n                    lineClassName={lineClassName}\n                    titleClassName={titleClassName}\n                    descriptionClassName={descriptionClassName}\n                    dateClassName={dateClassName}\n                  />\n                  <MobileTimelineEntry\n                    item={item}\n                    dotClassName={dotClassName}\n                    lineClassName={lineClassName}\n                    titleClassName={titleClassName}\n                    descriptionClassName={descriptionClassName}\n                    dateClassName={dateClassName}\n                  />\n                </motion.li>\n              ))}\n          </AnimatePresence>\n        </ul>\n      </div>\n      {remainingItems.length > 0 && (\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          className=\"mt-8 flex justify-center\"\n        >\n          <Button\n            variant={buttonVariant}\n            size={buttonSize}\n            className=\"gap-2\"\n            onClick={() => setShowAll(!showAll)}\n          >\n            {showAll ? showLessText : showMoreText}\n            <motion.div\n              animate={{ rotate: showAll ? 180 : 0 }}\n              transition={{ duration: 0.2 }}\n            >\n              <ChevronDown className=\"h-4 w-4\" />\n            </motion.div>\n          </Button>\n        </motion.div>\n      )}\n    </div>\n  );\n}\n"}], "dependencies": ["framer-motion", "lucide-react"]}