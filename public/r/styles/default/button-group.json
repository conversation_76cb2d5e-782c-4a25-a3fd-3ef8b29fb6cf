{"name": "button-group", "type": "registry:ui", "code": "\"use client\";\n\nimport { cn } from \"@/lib/utils\";\nimport { cva, VariantProps } from \"class-variance-authority\";\nimport React from \"react\";\n\nconst buttonGroupVariants = cva(\n  \"flex sm:items-center max-sm:gap-1 max-sm:flex-col [&>*:focus-within]:ring-1 [&>*:focus-within]:z-10 [&>*]:ring-offset-0 sm:[&>*:not(:first-child)]:rounded-l-none sm:[&>*:not(:last-child)]:rounded-r-none\",\n  {\n    variants: {\n      size: {\n        default: \"[&>*]:h-10 [&>*]:px-4 [&>*]:py-2\",\n        sm: \"[&>*]:h-9 [&>*]:rounded-md [&>*]:px-3\",\n        lg: \"[&>*]:h-11 [&>*]:rounded-md [&>*]:px-8\",\n        icon: \"[&>*]:h-10 [&>*]:w-10\",\n      },\n      separated: {\n        true: \"[&>*]:outline [&>*]:outline-1 [&>*]:outline-zinc-500 gap-0.5 [&>*:focus-within]:ring-offset-2\",\n        false: \"[&>*:focus-within]:ring-offset-1\",\n      },\n    },\n    defaultVariants: {\n      separated: false,\n      size: \"default\",\n    },\n  }\n);\n\nexport interface ButtonGroupProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof buttonGroupVariants> {\n  separated?: boolean;\n}\n\nconst ButtonGroup = React.forwardRef<HTMLDivElement, ButtonGroupProps>(\n  ({ children, className, size, separated = false, ...props }, ref) => {\n    return (\n      <div\n        className={cn(buttonGroupVariants({ size, className, separated }))}\n        ref={ref}\n        {...props}\n      >\n        {children}\n      </div>\n    );\n  }\n);\nButtonGroup.displayName = \"ButtonGroup\";\n\nexport { ButtonGroup };", "files": [{"path": "components/prismui/button-group.tsx", "type": "registry:ui", "content": "import { cn } from \"@/lib/utils\";\nimport { cva, VariantProps } from \"class-variance-authority\";\nimport React from \"react\";\n\nconst buttonGroupVariants = cva(\n  \"flex sm:items-center max-sm:gap-1 max-sm:flex-col [&>*:focus-within]:ring-1 [&>*:focus-within]:z-10 [&>*]:ring-offset-0 sm:[&>*:not(:first-child)]:rounded-l-none sm:[&>*:not(:last-child)]:rounded-r-none\",\n  {\n    variants: {\n      size: {\n        default: \"[&>*]:h-10 [&>*]:px-4 [&>*]:py-2\",\n        sm: \"[&>*]:h-9 [&>*]:rounded-md [&>*]:px-3\",\n        lg: \"[&>*]:h-11 [&>*]:rounded-md [&>*]:px-8\",\n        icon: \"[&>*]:h-10 [&>*]:w-10\",\n      },\n      separated: {\n        true: \"[&>*]:outline [&>*]:outline-1 [&>*]:outline-zinc-500 gap-0.5 [&>*:focus-within]:ring-offset-2\",\n        false: \"[&>*:focus-within]:ring-offset-1\",\n      },\n    },\n    defaultVariants: {\n      separated: false,\n      size: \"default\",\n    },\n  }\n);\n\nexport interface ButtonGroupProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof buttonGroupVariants> {\n  separated?: boolean;\n}\n\nconst ButtonGroup = React.forwardRef<HTMLDivElement, ButtonGroupProps>(\n  ({ children, className, size, separated = false, ...props }, ref) => {\n    return (\n      <div\n        className={cn(buttonGroupVariants({ size, className, separated }))}\n        ref={ref}\n        {...props}\n      >\n        {children}\n      </div>\n    );\n  }\n);\nButtonGroup.displayName = \"ButtonGroup\";\n\nexport { ButtonGroup };\n"}], "dependencies": ["class-variance-authority"]}