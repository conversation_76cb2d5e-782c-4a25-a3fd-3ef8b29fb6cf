{"name": "expandable-card", "type": "registry:ui", "code": "\"use client\";\n\nimport React, { useRef, useEffect } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport {\n  Clock,\n  GitBranch,\n  Github,\n  MessageSquare,\n  StepForwardIcon as Progress,\n  Star,\n  Users,\n  CheckCircle2,\n} from \"lucide-react\";\nimport {\n  <PERSON>,\n  CardContent,\n  <PERSON><PERSON>ooter,\n  CardHeader,\n} from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Button } from \"@/components/ui/button\";\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\nimport { Progress as ProgressBar } from \"@/components/ui/progress\";\nimport { useState, useCallback } from \"react\";\nimport { useSpring } from \"framer-motion\";\nimport {\n  Tooltip,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from \"@/components/ui/tooltip\";\n\n\ninterface ProjectStatusCardProps {\n  title: string;\n  progress: number;\n  dueDate: string;\n  contributors: Array<{ name: string; image?: string }>;\n  tasks: Array<{ title: string; completed: boolean }>;\n  githubStars: number;\n  openIssues: number;\n}\n\nexport function useExpandable(initialState = false) {\n  const [isExpanded, setIsExpanded] = useState(initialState);\n\n  const springConfig = { stiffness: 300, damping: 30 };\n  const animatedHeight = useSpring(0, springConfig);\n\n  const toggleExpand = useCallback(() => {\n    setIsExpanded((prev) => !prev);\n  }, []);\n\n  return { isExpanded, toggleExpand, animatedHeight };\n}\n\n\nexport function ProjectStatusCard({\n  title,\n  progress,\n  dueDate,\n  contributors,\n  tasks,\n  githubStars,\n  openIssues,\n}: ProjectStatusCardProps) {\n  const { isExpanded, toggleExpand, animatedHeight } = useExpandable();\n  const contentRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    if (contentRef.current) {\n      animatedHeight.set(isExpanded ? contentRef.current.scrollHeight : 0);\n    }\n  }, [isExpanded, animatedHeight]);\n\n  return (\n    <Card\n      className=\"w-full max-w-md cursor-pointer transition-all duration-300 hover:shadow-lg\"\n      onClick={toggleExpand}\n    >\n      <CardHeader className=\"space-y-1\">\n        <div className=\"flex justify-between items-start w-full\">\n          <div className=\"space-y-2\">\n            <Badge\n              variant=\"secondary\"\n              className={\n                progress === 100\n                  ? \"bg-green-100 text-green-600\"\n                  : \"bg-blue-100 text-blue-600\"\n              }\n            >\n              {progress === 100 ? \"Completed\" : \"In Progress\"}\n            </Badge>\n            <h3 className=\"text-2xl font-semibold\">{title}</h3>\n          </div>\n          <TooltipProvider>\n            <Tooltip>\n              <TooltipTrigger asChild>\n                <Button size=\"icon\" variant=\"outline\" className=\"h-8 w-8\">\n                  <Github className=\"h-4 w-4\" />\n                </Button>\n              </TooltipTrigger>\n              <TooltipContent>\n                <p>View on GitHub</p>\n              </TooltipContent>\n            </Tooltip>\n          </TooltipProvider>\n        </div>\n      </CardHeader>\n\n      <CardContent>\n        <div className=\"space-y-4\">\n          <div className=\"space-y-2\">\n            <div className=\"flex justify-between text-sm text-gray-600\">\n              <span>Progress</span>\n              <span>{progress}%</span>\n            </div>\n            <ProgressBar value={progress} className=\"h-2\" />\n          </div>\n\n          <motion.div\n            style={{ height: animatedHeight }}\n            transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\n            className=\"overflow-hidden\"\n          >\n            <div ref={contentRef}>\n              <AnimatePresence>\n                {isExpanded && (\n                  <motion.div\n                    initial={{ opacity: 0 }}\n                    animate={{ opacity: 1 }}\n                    exit={{ opacity: 0 }}\n                    className=\"space-y-4 pt-2\"\n                  >\n                    <div className=\"flex items-center justify-between text-sm text-gray-600\">\n                      <div className=\"flex items-center\">\n                        <Clock className=\"h-4 w-4 mr-2\" />\n                        <span>Due {dueDate}</span>\n                      </div>\n                      <div className=\"flex items-center gap-4\">\n                        <div className=\"flex items-center\">\n                          <Star className=\"h-4 w-4 mr-1 text-yellow-400\" />\n                          <span>{githubStars}</span>\n                        </div>\n                        <div className=\"flex items-center\">\n                          <GitBranch className=\"h-4 w-4 mr-1\" />\n                          <span>{openIssues} issues</span>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"space-y-2\">\n                      <h4 className=\"font-medium text-sm flex items-center\">\n                        <Users className=\"h-4 w-4 mr-2\" />\n                        Contributors\n                      </h4>\n                      <div className=\"flex -space-x-2\">\n                        {contributors.map((contributor, index) => (\n                          <TooltipProvider key={index}>\n                            <Tooltip>\n                              <TooltipTrigger asChild>\n                                <Avatar className=\"border-2 border-white\">\n                                  <AvatarImage\n                                    src={\n                                      contributor.image ||\n                                      `/placeholder.svg?height=32&width=32&text=${contributor.name[0]}`\n                                    }\n                                    alt={contributor.name}\n                                  />\n                                  <AvatarFallback>\n                                    {contributor.name[0]}\n                                  </AvatarFallback>\n                                </Avatar>\n                              </TooltipTrigger>\n                              <TooltipContent>\n                                <p>{contributor.name}</p>\n                              </TooltipContent>\n                            </Tooltip>\n                          </TooltipProvider>\n                        ))}\n                      </div>\n                    </div>\n\n                    <div className=\"space-y-2\">\n                      <h4 className=\"font-medium text-sm\">Recent Tasks</h4>\n                      {tasks.map((task, index) => (\n                        <div\n                          key={index}\n                          className=\"flex items-center justify-between text-sm\"\n                        >\n                          <span className=\"text-gray-600\">{task.title}</span>\n                          {task.completed && (\n                            <CheckCircle2 className=\"h-4 w-4 text-green-500\" />\n                          )}\n                        </div>\n                      ))}\n                    </div>\n\n                    <div className=\"space-y-2\">\n                      <Button className=\"w-full\">\n                        <MessageSquare className=\"h-4 w-4 mr-2\" />\n                        View Discussion\n                      </Button>\n                    </div>\n                  </motion.div>\n                )}\n              </AnimatePresence>\n            </div>\n          </motion.div>\n        </div>\n      </CardContent>\n\n      <CardFooter>\n        <div className=\"flex items-center justify-between w-full text-sm text-gray-600\">\n          <span>Last updated: 2 hours ago</span>\n          <span>{openIssues} open issues</span>\n        </div>\n      </CardFooter>\n    </Card>\n  );\n}", "files": [{"path": "components/prismui/expandable-card.tsx", "type": "registry:ui", "content": "\"use client\";\n\nimport React, { useRef, useEffect,useState } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport {\n  Clock,\n  GitBranch,\n  Github,\n  MessageSquare,\n  StepForwardIcon as Progress,\n  Star,\n  Users,\n  CheckCircle2,\n} from \"lucide-react\";\nimport {\n  <PERSON>,\n  CardContent,\n  <PERSON>Footer,\n  CardHeader,\n} from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Button } from \"@/components/ui/button\";\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\nimport { Progress as ProgressBar } from \"@/components/ui/progress\";\nimport {\n  Tooltip,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from \"@/components/ui/tooltip\";\nimport { useExpandable } from \"@/hooks/use-expandable\";\n\ninterface ProjectStatusCardProps {\n  title: string;\n  progress: number;\n  dueDate: string;\n  contributors: Array<{ name: string; image?: string }>;\n  tasks: Array<{ title: string; completed: boolean }>;\n  githubStars: number;\n  openIssues: number;\n}\n\nexport function ProjectStatusCard({\n  title,\n  progress,\n  dueDate,\n  contributors,\n  tasks,\n  githubStars,\n  openIssues,\n}: ProjectStatusCardProps) {\n  const { isExpanded, toggleExpand, animatedHeight } = useExpandable();\n  const contentRef = useRef<HTMLDivElement>(null);\n  const [width, setWidth] = useState(0);\n\n  useEffect(() => {\n    const updateWidth = () => {\n      if (contentRef.current) {\n        setWidth(contentRef.current.offsetWidth);\n      }\n    };\n    updateWidth();\n    window.addEventListener(\"resize\", updateWidth);\n    return () => {window.removeEventListener(\"resize\", updateWidth);};\n  }, []);\n\n  useEffect(() => {\n    if (contentRef.current) {\n      animatedHeight.set(isExpanded ? contentRef.current.scrollHeight : 0);\n    }\n  }, [isExpanded, animatedHeight]);\n\n  return (\n    <Card\n      className=\"w-full max-w-md cursor-pointer transition-all duration-300 hover:shadow-lg\"\n      onClick={toggleExpand}\n    >\n      <CardHeader className=\"space-y-1\">\n        <div className=\"flex justify-between items-start w-full\">\n          <div className=\"space-y-2\">\n            <Badge\n              variant=\"secondary\"\n              className={\n                progress === 100\n                  ? \"bg-green-100 text-green-600\"\n                  : \"bg-blue-100 text-blue-600\"\n              }\n            >\n              {progress === 100 ? \"Completed\" : \"In Progress\"}\n            </Badge>\n            <h3 className=\"text-2xl font-semibold\">{title}</h3>\n          </div>\n          <TooltipProvider>\n            <Tooltip>\n              <TooltipTrigger asChild>\n                <Button size=\"icon\" variant=\"outline\" className=\"h-8 w-8\">\n                  <Github className=\"h-4 w-4\" />\n                </Button>\n              </TooltipTrigger>\n              <TooltipContent>\n                <p>View on GitHub</p>\n              </TooltipContent>\n            </Tooltip>\n          </TooltipProvider>\n        </div>\n      </CardHeader>\n\n      <CardContent>\n        <div className=\"space-y-4\">\n          <div className=\"space-y-2\">\n            <div className=\"flex justify-between text-sm text-gray-600\">\n              <span>Progress</span>\n              <span>{progress}%</span>\n            </div>\n            <ProgressBar value={progress} className=\"h-2\" />\n          </div>\n\n          <motion.div\n            style={{ height: animatedHeight }}\n            transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\n            className=\"overflow-hidden\"\n          >\n            <div ref={contentRef}>\n              <AnimatePresence>\n                {isExpanded && (\n                  <motion.div\n                    initial={{ opacity: 0 }}\n                    animate={{ opacity: 1 }}\n                    exit={{ opacity: 0 }}\n                    className=\"space-y-4 pt-2\"\n                  >\n                    <div className=\"flex items-center justify-between text-sm text-gray-600\">\n                      <div className=\"flex items-center\">\n                        <Clock className=\"h-4 w-4 mr-2\" />\n                        <span>Due {dueDate}</span>\n                      </div>\n                      <div className=\"flex items-center gap-4\">\n                        <div className=\"flex items-center\">\n                          <Star className=\"h-4 w-4 mr-1 text-yellow-400\" />\n                          <span>{githubStars}</span>\n                        </div>\n                        <div className=\"flex items-center\">\n                          <GitBranch className=\"h-4 w-4 mr-1\" />\n                          <span>{openIssues} issues</span>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"space-y-2\">\n                      <h4 className=\"font-medium text-sm flex items-center\">\n                        <Users className=\"h-4 w-4 mr-2\" />\n                        Contributors\n                      </h4>\n                      <div className=\"flex -space-x-2\">\n                        {contributors.map((contributor, index) => (\n                          <TooltipProvider key={index}>\n                            <Tooltip>\n                              <TooltipTrigger asChild>\n                                <Avatar className=\"border-2 border-white\">\n                                  <AvatarImage\n                                    src={\n                                      contributor.image ||\n                                      `/placeholder.svg?height=32&width=32&text=${contributor.name[0]}`\n                                    }\n                                    alt={contributor.name}\n                                  />\n                                  <AvatarFallback>\n                                    {contributor.name[0]}\n                                  </AvatarFallback>\n                                </Avatar>\n                              </TooltipTrigger>\n                              <TooltipContent>\n                                <p>{contributor.name}</p>\n                              </TooltipContent>\n                            </Tooltip>\n                          </TooltipProvider>\n                        ))}\n                      </div>\n                    </div>\n\n                    <div className=\"space-y-2\">\n                      <h4 className=\"font-medium text-sm\">Recent Tasks</h4>\n                      {tasks.map((task, index) => (\n                        <div\n                          key={index}\n                          className=\"flex items-center justify-between text-sm\"\n                        >\n                          <span className=\"text-gray-600\">{task.title}</span>\n                          {task.completed && (\n                            <CheckCircle2 className=\"h-4 w-4 text-green-500\" />\n                          )}\n                        </div>\n                      ))}\n                    </div>\n\n                    <div className=\"space-y-2\">\n                      <Button className=\"w-full\">\n                        <MessageSquare className=\"h-4 w-4 mr-2\" />\n                        View Discussion\n                      </Button>\n                    </div>\n                  </motion.div>\n                )}\n              </AnimatePresence>\n            </div>\n          </motion.div>\n        </div>\n      </CardContent>\n\n      <CardFooter>\n        <div className=\"flex items-center justify-between w-full text-sm gap-3 text-gray-600 flex-wrap\">\n          <span>Last updated: 2 hours ago</span>\n          {width < 300 &&  <span >/</span>}\n          <span>{openIssues} open issues</span>\n        </div>\n\n      </CardFooter>\n    </Card>\n  );\n}\n"}], "dependencies": ["framer-motion", "lucide-react"]}