{"name": "logo-carousel", "type": "registry:ui", "code": "\"use client\";\n\nimport { useCallback, useEffect, useMemo, useState } from \"react\";\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport type { SVGProps } from \"react\";\nimport Image from \"next/image\";\n\n// Define types\ninterface Logo {\n  id: number;\n  name: string;\n  src: string;\n}\n\ninterface LogoColumnProps {\n  logos: Logo[];\n  columnIndex: number;\n  currentTime: number;\n}\n\n// Main component\nexport function LogoCarousel({ columns = 2 }: { columns?: number }) {\n  const [logoColumns, setLogoColumns] = useState<Logo[][]>([]);\n  const [time, setTime] = useState(0);\n  const CYCLE_DURATION = 2000; // 2 seconds per logo\n\n  // Define logos using public SVGs\n  const logos = useMemo<Logo[]>(\n    () => [\n      { id: 1, name: \"Dub\", src: \"/logo/dub.svg\" },\n      { id: 2, name: \"Supabase\", src: \"/logo/supabase.svg\" },\n      { id: 3, name: \"Vercel\", src: \"/logo/vercel.svg\" },\n      { id: 4, name: \"Resend\", src: \"/logo/resend.svg\" },\n      { id: 5, name: \"Shadcn\", src: \"/logo/shadcn.svg\" },\n    ],\n    []\n  );\n\n  // Distribute logos across columns\n  const distributeLogos = useCallback(\n    (logos: Logo[]) => {\n      const shuffled = [...logos].sort(() => Math.random() - 0.5);\n      const result: Logo[][] = Array.from({ length: columns }, () => []);\n\n      shuffled.forEach((logo, index) => {\n        result[index % columns].push(logo);\n      });\n\n      // Ensure equal length columns\n      const maxLength = Math.max(...result.map((col) => col.length));\n      result.forEach((col) => {\n        while (col.length < maxLength) {\n          col.push(shuffled[Math.floor(Math.random() * shuffled.length)]);\n        }\n      });\n\n      return result;\n    },\n    [columns]\n  );\n\n  // Initialize logo columns\n  useEffect(() => {\n    setLogoColumns(distributeLogos(logos));\n  }, [logos, distributeLogos]);\n\n  // Update time for animation\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setTime((prev) => prev + 100);\n    }, 100);\n    return () => clearInterval(interval);\n  }, []);\n\n  return (\n    <div className=\"flex justify-center gap-4 py-8\">\n      {logoColumns.map((columnLogos, index) => (\n        <LogoColumn\n          key={index}\n          logos={columnLogos}\n          columnIndex={index}\n          currentTime={time}\n        />\n      ))}\n    </div>\n  );\n}\n\n// Column component\nfunction LogoColumn({ logos, columnIndex, currentTime }: LogoColumnProps) {\n  const CYCLE_DURATION = 2000;\n  const columnDelay = columnIndex * 200;\n  const adjustedTime =\n    (currentTime + columnDelay) % (CYCLE_DURATION * logos.length);\n  const currentIndex = Math.floor(adjustedTime / CYCLE_DURATION);\n  const currentLogo = logos[currentIndex];\n\n  return (\n    <motion.div\n      className=\"relative h-14 w-24 overflow-hidden md:h-24 md:w-48\"\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{\n        delay: columnIndex * 0.1,\n        duration: 0.5,\n        ease: \"easeOut\",\n      }}\n    >\n      <AnimatePresence mode=\"wait\">\n        <motion.div\n          key={`${currentLogo.id}-${currentIndex}`}\n          className=\"absolute inset-0 flex items-center justify-center\"\n          initial={{ y: \"10%\", opacity: 0 }}\n          animate={{\n            y: \"0%\",\n            opacity: 1,\n            transition: {\n              type: \"spring\",\n              stiffness: 300,\n              damping: 20,\n            },\n          }}\n          exit={{\n            y: \"-20%\",\n            opacity: 0,\n            transition: { duration: 0.3 },\n          }}\n        >\n          <Image\n            src={currentLogo.src}\n            alt={currentLogo.name}\n            width={120}\n            height={40}\n            className=\"h-auto w-auto max-h-[80%] max-w-[80%] object-contain\"\n          />\n        </motion.div>\n      </AnimatePresence>\n    </motion.div>\n  );\n}", "files": [{"path": "components/prismui/logo-carousel.tsx", "type": "registry:ui", "content": "\"use client\";\n\nimport { useCallback, useEffect, useMemo, useState } from \"react\";\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport type { SVGProps } from \"react\";\nimport Image from \"next/image\";\n\n// Define types\ninterface Logo {\n  id: number;\n  name: string;\n  src: string;\n}\n\ninterface LogoColumnProps {\n  logos: Logo[];\n  columnIndex: number;\n  currentTime: number;\n}\n\n// Main component\nexport function LogoCarousel({ columns = 2 }: { columns?: number }) {\n  const [logoColumns, setLogoColumns] = useState<Logo[][]>([]);\n  const [time, setTime] = useState(0);\n  const CYCLE_DURATION = 2000; // 2 seconds per logo\n\n  // Define logos using public SVGs\n  const logos = useMemo<Logo[]>(\n    () => [\n      { id: 1, name: \"Dub\", src: \"/logo/dub.svg\" },\n      { id: 2, name: \"Supabase\", src: \"/logo/supabase.svg\" },\n      { id: 3, name: \"Vercel\", src: \"/logo/vercel.svg\" },\n      { id: 4, name: \"Resend\", src: \"/logo/resend.svg\" },\n      { id: 5, name: \"Shadcn\", src: \"/logo/shadcn.svg\" },\n    ],\n    []\n  );\n\n  // Distribute logos across columns\n  const distributeLogos = useCallback(\n    (logos: Logo[]) => {\n      const shuffled = [...logos].sort(() => Math.random() - 0.5);\n      const result: Logo[][] = Array.from({ length: columns }, () => []);\n\n      shuffled.forEach((logo, index) => {\n        result[index % columns].push(logo);\n      });\n\n      // Ensure equal length columns\n      const maxLength = Math.max(...result.map((col) => col.length));\n      result.forEach((col) => {\n        while (col.length < maxLength) {\n          col.push(shuffled[Math.floor(Math.random() * shuffled.length)]);\n        }\n      });\n\n      return result;\n    },\n    [columns]\n  );\n\n  // Initialize logo columns\n  useEffect(() => {\n    setLogoColumns(distributeLogos(logos));\n  }, [logos, distributeLogos]);\n\n  // Update time for animation\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setTime((prev) => prev + 100);\n    }, 100);\n    return () => clearInterval(interval);\n  }, []);\n\n  return (\n    <div className=\"flex justify-center gap-4 py-8\">\n      {logoColumns.map((columnLogos, index) => (\n        <LogoColumn\n          key={index}\n          logos={columnLogos}\n          columnIndex={index}\n          currentTime={time}\n        />\n      ))}\n    </div>\n  );\n}\n\n// Column component\nfunction LogoColumn({ logos, columnIndex, currentTime }: LogoColumnProps) {\n  const CYCLE_DURATION = 2000;\n  const columnDelay = columnIndex * 200;\n  const adjustedTime =\n    (currentTime + columnDelay) % (CYCLE_DURATION * logos.length);\n  const currentIndex = Math.floor(adjustedTime / CYCLE_DURATION);\n  const currentLogo = logos[currentIndex];\n\n  return (\n    <motion.div\n      className=\"relative h-14 w-24 overflow-hidden md:h-24 md:w-48\"\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{\n        delay: columnIndex * 0.1,\n        duration: 0.5,\n        ease: \"easeOut\",\n      }}\n    >\n      <AnimatePresence mode=\"wait\">\n        <motion.div\n          key={`${currentLogo.id}-${currentIndex}`}\n          className=\"absolute inset-0 flex items-center justify-center\"\n          initial={{ y: \"10%\", opacity: 0 }}\n          animate={{\n            y: \"0%\",\n            opacity: 1,\n            transition: {\n              type: \"spring\",\n              stiffness: 300,\n              damping: 20,\n            },\n          }}\n          exit={{\n            y: \"-20%\",\n            opacity: 0,\n            transition: { duration: 0.3 },\n          }}\n        >\n          <Image\n            src={currentLogo.src}\n            alt={currentLogo.name}\n            width={120}\n            height={40}\n            className=\"h-auto w-auto max-h-[80%] max-w-[80%] object-contain\"\n          />\n        </motion.div>\n      </AnimatePresence>\n    </motion.div>\n  );\n}\n"}], "dependencies": ["framer-motion"]}