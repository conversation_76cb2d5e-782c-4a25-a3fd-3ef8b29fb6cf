{"name": "hero-badge", "type": "registry:ui", "code": "\"use client\";\n\nimport { motion, useAnimation, type Variants } from \"framer-motion\";\nimport Link from \"next/link\";\nimport { cn } from \"@/lib/utils\";\n\nconst ease = [0.16, 1, 0.3, 1];\n\ninterface HeroBadgeProps {\n  href?: string;\n  text: string;\n  icon?: React.ReactNode;\n  endIcon?: React.ReactNode;\n  variant?: \"default\" | \"outline\" | \"ghost\";\n  size?: \"sm\" | \"md\" | \"lg\";\n  className?: string;\n  onClick?: () => void;\n}\n\nconst badgeVariants: Record<string, string> = {\n  default: \"bg-background hover:bg-muted\",\n  outline: \"border-2 hover:bg-muted\",\n  ghost: \"hover:bg-muted/50\",\n};\n\nconst sizeVariants: Record<string, string> = {\n  sm: \"px-3 py-1 text-xs gap-1.5\",\n  md: \"px-4 py-1.5 text-sm gap-2\",\n  lg: \"px-5 py-2 text-base gap-2.5\",\n};\n\nconst iconAnimationVariants: Variants = {\n  initial: { rotate: 0 },\n  hover: { rotate: -10 },\n};\n\nexport default function HeroBadge({\n  href,\n  text,\n  icon,\n  endIcon,\n  variant = \"default\",\n  size = \"md\",\n  className,\n  onClick,\n}: HeroBadgeProps) {\n  const controls = useAnimation();\n\n  const BadgeWrapper = href ? Link : motion.button;\n  const wrapperProps = href ? { href } : { onClick };\n\n  const baseClassName = cn(\n    \"inline-flex items-center rounded-full border transition-colors\",\n    badgeVariants[variant],\n    sizeVariants[size],\n    className\n  );\n\n  return (\n    <BadgeWrapper\n      {...wrapperProps}\n      className={cn(\"group\", href && \"cursor-pointer\")}\n    >\n      <motion.div\n        className={baseClassName}\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.8, ease }}\n        onHoverStart={() => controls.start(\"hover\")}\n        onHoverEnd={() => controls.start(\"initial\")}\n      >\n        {icon && (\n          <motion.div\n            className=\"text-foreground/60 transition-colors group-hover:text-primary\"\n            variants={iconAnimationVariants}\n            initial=\"initial\"\n            animate={controls}\n            transition={{ type: \"spring\", stiffness: 300, damping: 10 }}\n          >\n            {icon}\n          </motion.div>\n        )}\n        <span>{text}</span>\n        {endIcon && (\n          <motion.div className=\"text-foreground/60\">{endIcon}</motion.div>\n        )}\n      </motion.div>\n    </BadgeWrapper>\n  );\n}", "files": [{"path": "components/prismui/hero-badge.tsx", "type": "registry:ui", "content": "\"use client\";\n\nimport { motion, useAnimation, type Variants } from \"framer-motion\";\nimport Link from \"next/link\";\nimport { cn } from \"@/lib/utils\";\n\nconst ease = [0.16, 1, 0.3, 1];\n\ninterface HeroBadgeProps {\n  href?: string;\n  text: string;\n  icon?: React.ReactNode;\n  endIcon?: React.ReactNode;\n  variant?: \"default\" | \"outline\" | \"ghost\";\n  size?: \"sm\" | \"md\" | \"lg\";\n  className?: string;\n  onClick?: () => void;\n}\n\nconst badgeVariants: Record<string, string> = {\n  default: \"bg-background hover:bg-muted\",\n  outline: \"border-2 hover:bg-muted\",\n  ghost: \"hover:bg-muted/50\",\n};\n\nconst sizeVariants: Record<string, string> = {\n  sm: \"px-3 py-1 text-xs gap-1.5\",\n  md: \"px-4 py-1.5 text-sm gap-2\",\n  lg: \"px-5 py-2 text-base gap-2.5\",\n};\n\nconst iconAnimationVariants: Variants = {\n  initial: { rotate: 0 },\n  hover: { rotate: -10 },\n};\n\nexport default function HeroBadge({\n  href,\n  text,\n  icon,\n  endIcon,\n  variant = \"default\",\n  size = \"md\",\n  className,\n  onClick,\n}: HeroBadgeProps) {\n  const controls = useAnimation();\n\n  const BadgeWrapper = href ? Link : motion.button;\n  const wrapperProps = href ? { href } : { onClick };\n\n  const baseClassName = cn(\n    \"inline-flex items-center rounded-full border transition-colors\",\n    badgeVariants[variant],\n    sizeVariants[size],\n    className\n  );\n\n  return (\n    <BadgeWrapper\n      {...wrapperProps}\n      className={cn(\"group\", href && \"cursor-pointer\")}\n    >\n      <motion.div\n        className={baseClassName}\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.8, ease }}\n        onHoverStart={() => controls.start(\"hover\")}\n        onHoverEnd={() => controls.start(\"initial\")}\n      >\n        {icon && (\n          <motion.div\n            className=\"text-foreground/60 transition-colors group-hover:text-primary\"\n            variants={iconAnimationVariants}\n            initial=\"initial\"\n            animate={controls}\n            transition={{ type: \"spring\", stiffness: 300, damping: 10 }}\n          >\n            {icon}\n          </motion.div>\n        )}\n        <span>{text}</span>\n        {endIcon && (\n          <motion.div className=\"text-foreground/60\">{endIcon}</motion.div>\n        )}\n      </motion.div>\n    </BadgeWrapper>\n  );\n}\n"}], "dependencies": ["framer-motion"]}