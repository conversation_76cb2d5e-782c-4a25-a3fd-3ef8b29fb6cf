{"name": "pricing", "type": "registry:ui", "code": "\"use client\";\n\n// ... (paste the entire component code here)\n", "files": [{"path": "components/prismui/pricing.tsx", "type": "registry:ui", "content": "\"use client\";\n\nimport { useState } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Switch } from \"@/components/ui/switch\";\nimport { Check } from \"lucide-react\";\nimport { cn } from \"@/lib/utils\";\nimport NumberFlow from \"@/components/prismui/number-flow\";\nimport confetti from \"canvas-confetti\";\n\ninterface PricingPlan {\n  name: string;\n  description: string;\n  price: {\n    monthly: number;\n    yearly: number;\n  };\n  features: string[];\n  isPopular?: boolean;\n  buttonText?: string;\n  buttonVariant?: \"default\" | \"outline\";\n}\n\ninterface PricingProps {\n  title?: string;\n  description?: string;\n  plans: PricingPlan[];\n  className?: string;\n}\n\nexport default function Pricing({\n  title = \"Simple, transparent pricing\",\n  description = \"Choose the plan that's right for you\",\n  plans,\n  className,\n}: PricingProps) {\n  const [isYearly, setIsYearly] = useState(false);\n\n  const handleToggle = () => {\n    setIsYearly(!isYearly);\n    if (!isYearly) {\n      confetti({\n        particleCount: 100,\n        spread: 70,\n        origin: { y: 0.6 },\n      });\n    }\n  };\n\n  return (\n    <section className={cn(\"py-16 md:py-24\", className)}>\n      <div className=\"container px-4 md:px-6\">\n        <div className=\"flex flex-col items-center space-y-4 text-center\">\n          <h2 className=\"text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl\">\n            {title}\n          </h2>\n          <p className=\"max-w-[700px] text-gray-500 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed dark:text-gray-400\">\n            {description}\n          </p>\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-sm font-medium\">Monthly</span>\n            <Switch checked={isYearly} onCheckedChange={handleToggle} />\n            <span className=\"text-sm font-medium\">Yearly</span>\n            {isYearly && (\n              <Badge variant=\"secondary\" className=\"ml-2\">\n                Save 20%\n              </Badge>\n            )}\n          </div>\n        </div>\n        <div className=\"grid grid-cols-1 gap-6 mt-12 md:grid-cols-3 lg:gap-8\">\n          {plans.map((plan, index) => (\n            <motion.div\n              key={plan.name}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: index * 0.1 }}\n              className=\"relative\"\n            >\n              <Card\n                className={cn(\n                  \"flex h-full flex-col p-6\",\n                  plan.isPopular && \"border-primary shadow-lg\"\n                )}\n              >\n                {plan.isPopular && (\n                  <Badge\n                    className=\"absolute -top-2 right-4\"\n                    variant=\"secondary\"\n                  >\n                    Most Popular\n                  </Badge>\n                )}\n                <div className=\"flex-1 space-y-4\">\n                  <h3 className=\"text-xl font-bold\">{plan.name}</h3>\n                  <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n                    {plan.description}\n                  </p>\n                  <div className=\"flex items-baseline\">\n                    <span className=\"text-3xl font-bold\">$</span>\n                    <AnimatePresence mode=\"wait\">\n                      <NumberFlow\n                        key={isYearly ? \"yearly\" : \"monthly\"}\n                        value={\n                          isYearly ? plan.price.yearly : plan.price.monthly\n                        }\n                        format={{ minimumFractionDigits: 0 }}\n                      />\n                    </AnimatePresence>\n                    <span className=\"ml-1 text-sm text-gray-500\">\n                      /{isYearly ? \"year\" : \"month\"}\n                    </span>\n                  </div>\n                  <ul className=\"space-y-2\">\n                    {plan.features.map((feature) => (\n                      <li\n                        key={feature}\n                        className=\"flex items-center text-sm text-gray-500\"\n                      >\n                        <Check className=\"mr-2 h-4 w-4 text-primary\" />\n                        {feature}\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n                <Button\n                  className=\"mt-6 w-full\"\n                  variant={plan.buttonVariant || \"default\"}\n                >\n                  {plan.buttonText || \"Get Started\"}\n                </Button>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n"}], "dependencies": ["framer-motion", "canvas-confetti"]}