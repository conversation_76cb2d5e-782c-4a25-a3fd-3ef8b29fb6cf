[{"name": "word-reveal", "type": "registry:ui", "code": "\"use client\";\n\nimport { motion, Variants } from \"framer-motion\";\nimport { cn } from \"@/lib/utils\";\n\ninterface WordRevealProps {\n  text: string;\n  className?: string;\n  delay?: number;\n}\n\nexport default function WordReveal({\n  text,\n  className,\n  delay = 0.15,\n}: WordRevealProps) {\n  const words = text.split(\" \");\n\n  const container: Variants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: { staggerChildren: delay },\n    },\n  };\n\n  const child: Variants = {\n    hidden: {\n      opacity: 0,\n      filter: \"blur(10px)\",\n      y: 20,\n    },\n    visible: (i: number) => ({\n      opacity: 1,\n      filter: \"blur(0px)\",\n      y: 0,\n      transition: {\n        delay: i * delay,\n        type: \"spring\",\n        damping: 12,\n        stiffness: 100,\n      },\n    }),\n  };\n\n  return (\n    <motion.h1\n      variants={container}\n      initial=\"hidden\"\n      animate=\"visible\"\n      className={cn(\n        \"font-display text-center text-4xl font-bold tracking-[-0.02em] text-white drop-shadow-sm md:text-7xl md:leading-[5rem]\",\n        className\n      )}\n    >\n      {words.map((word, i) => (\n        <motion.span\n          key={word + i}\n          variants={child}\n          custom={i}\n          className=\"inline-block mr-[0.25em] last:mr-0\"\n        >\n          {word}\n        </motion.span>\n      ))}\n    </motion.h1>\n  );\n}", "files": [{"path": "components/prismui/word-reveal.tsx", "type": "registry:ui", "content": "\"use client\";\n\nimport { motion, Variants } from \"framer-motion\";\nimport { cn } from \"@/lib/utils\";\n\ninterface WordRevealProps {\n  text: string;\n  className?: string;\n  delay?: number;\n}\n\nexport default function WordReveal({\n  text,\n  className,\n  delay = 0.15,\n}: WordRevealProps) {\n  const words = text.split(\" \");\n\n  const container: Variants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: { staggerChildren: delay },\n    },\n  };\n\n  const child: Variants = {\n    hidden: {\n      opacity: 0,\n      filter: \"blur(10px)\",\n      y: 20,\n    },\n    visible: (i: number) => ({\n      opacity: 1,\n      filter: \"blur(0px)\",\n      y: 0,\n      transition: {\n        delay: i * delay,\n        type: \"spring\",\n        damping: 12,\n        stiffness: 100,\n      },\n    }),\n  };\n\n  return (\n    <motion.h1\n      variants={container}\n      initial=\"hidden\"\n      animate=\"visible\"\n      className={cn(\n        \"font-display text-center text-4xl font-bold tracking-[-0.02em] text-white drop-shadow-sm md:text-7xl md:leading-[5rem]\",\n        className\n      )}\n    >\n      {words.map((word, i) => (\n        <motion.span\n          key={word + i}\n          variants={child}\n          custom={i}\n          className=\"inline-block mr-[0.25em] last:mr-0\"\n        >\n          {word}\n        </motion.span>\n      ))}\n    </motion.h1>\n  );\n}"}], "dependencies": ["framer-motion"], "category": "components", "subcategory": "animation"}, {"name": "card", "type": "registry:ui", "code": "\"use client\";\n\nimport * as React from \"react\";\nimport { cn } from \"@/lib/utils\";\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  /** Optional hover effect */\n  hover?: boolean;\n  /** Optional gradient background */\n  gradient?: boolean;\n  /** Optional border style */\n  bordered?: boolean;\n}\n\nexport default function Card({\n  className,\n  hover = false,\n  gradient = false,\n  bordered = false,\n  children,\n  ...props\n}: CardProps) {\n  return (\n    <div\n      className={cn(\n        \"rounded-lg bg-card p-6\",\n        {\n          \"transition-all duration-200 hover:scale-[1.02] hover:shadow-lg\": hover,\n          \"bg-gradient-to-br from-card/50 to-card\": gradient,\n          \"border border-border\": bordered,\n        },\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n}\n\ninterface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {}\n\nexport function CardHeader({ className, ...props }: CardHeaderProps) {\n  return <div className={cn(\"mb-4\", className)} {...props} />;\n}\n\ninterface CardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {}\n\nexport function CardTitle({ className, ...props }: CardTitleProps) {\n  return (\n    <h3\n      className={cn(\"text-2xl font-semibold tracking-tight\", className)}\n      {...props}\n    />\n  );\n}\n\ninterface CardDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {}\n\nexport function CardDescription({ className, ...props }: CardDescriptionProps) {\n  return (\n    <p\n      className={cn(\"text-sm text-muted-foreground\", className)}\n      {...props}\n    />\n  );\n}\n\ninterface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {}\n\nexport function CardContent({ className, ...props }: CardContentProps) {\n  return <div className={cn(\"\", className)} {...props} />;\n}\n\ninterface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {}\n\nexport function CardFooter({ className, ...props }: CardFooterProps) {\n  return (\n    <div\n      className={cn(\"mt-4 flex items-center justify-between\", className)}\n      {...props}\n    />\n  );\n}", "files": [{"path": "components/prismui/card.tsx", "type": "registry:ui", "content": "\"use client\";\n\nimport * as React from \"react\";\nimport { cn } from \"@/lib/utils\";\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  /** Optional hover effect */\n  hover?: boolean;\n  /** Optional gradient background */\n  gradient?: boolean;\n  /** Optional border style */\n  bordered?: boolean;\n}\n\nexport default function Card({\n  className,\n  hover = false,\n  gradient = false,\n  bordered = false,\n  children,\n  ...props\n}: CardProps) {\n  return (\n    <div\n      className={cn(\n        \"rounded-lg bg-card p-6\",\n        {\n          \"transition-all duration-200 hover:scale-[1.02] hover:shadow-lg\": hover,\n          \"bg-gradient-to-br from-card/50 to-card\": gradient,\n          \"border border-border\": bordered,\n        },\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n}\n\ninterface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {}\n\nexport function CardHeader({ className, ...props }: CardHeaderProps) {\n  return <div className={cn(\"mb-4\", className)} {...props} />;\n}\n\ninterface CardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {}\n\nexport function CardTitle({ className, ...props }: CardTitleProps) {\n  return (\n    <h3\n      className={cn(\"text-2xl font-semibold tracking-tight\", className)}\n      {...props}\n    />\n  );\n}\n\ninterface CardDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {}\n\nexport function CardDescription({ className, ...props }: CardDescriptionProps) {\n  return (\n    <p\n      className={cn(\"text-sm text-muted-foreground\", className)}\n      {...props}\n    />\n  );\n}\n\ninterface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {}\n\nexport function CardContent({ className, ...props }: CardContentProps) {\n  return <div className={cn(\"\", className)} {...props} />;\n}\n\ninterface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {}\n\nexport function CardFooter({ className, ...props }: CardFooterProps) {\n  return (\n    <div\n      className={cn(\"mt-4 flex items-center justify-between\", className)}\n      {...props}\n    />\n  );\n}"}], "dependencies": [], "category": "components", "subcategory": "layout"}, {"name": "logo-carousel", "type": "registry:ui", "code": "\"use client\";\n\nimport { useCallback, useEffect, useMemo, useState } from \"react\";\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport type { SVGProps } from \"react\";\nimport Image from \"next/image\";\n\n// Define types\ninterface Logo {\n  id: number;\n  name: string;\n  src: string;\n}\n\ninterface LogoColumnProps {\n  logos: Logo[];\n  columnIndex: number;\n  currentTime: number;\n}\n\n// Main component\nexport function LogoCarousel({ columns = 2 }: { columns?: number }) {\n  const [logoColumns, setLogoColumns] = useState<Logo[][]>([]);\n  const [time, setTime] = useState(0);\n  const CYCLE_DURATION = 2000; // 2 seconds per logo\n\n  // Define logos using public SVGs\n  const logos = useMemo<Logo[]>(\n    () => [\n      { id: 1, name: \"Dub\", src: \"/logo/dub.svg\" },\n      { id: 2, name: \"Supabase\", src: \"/logo/supabase.svg\" },\n      { id: 3, name: \"Vercel\", src: \"/logo/vercel.svg\" },\n      { id: 4, name: \"Resend\", src: \"/logo/resend.svg\" },\n      { id: 5, name: \"Shadcn\", src: \"/logo/shadcn.svg\" },\n    ],\n    []\n  );\n\n  // Distribute logos across columns\n  const distributeLogos = useCallback(\n    (logos: Logo[]) => {\n      const shuffled = [...logos].sort(() => Math.random() - 0.5);\n      const result: Logo[][] = Array.from({ length: columns }, () => []);\n\n      shuffled.forEach((logo, index) => {\n        result[index % columns].push(logo);\n      });\n\n      // Ensure equal length columns\n      const maxLength = Math.max(...result.map((col) => col.length));\n      result.forEach((col) => {\n        while (col.length < maxLength) {\n          col.push(shuffled[Math.floor(Math.random() * shuffled.length)]);\n        }\n      });\n\n      return result;\n    },\n    [columns]\n  );\n\n  // Initialize logo columns\n  useEffect(() => {\n    setLogoColumns(distributeLogos(logos));\n  }, [logos, distributeLogos]);\n\n  // Update time for animation\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setTime((prev) => prev + 100);\n    }, 100);\n    return () => clearInterval(interval);\n  }, []);\n\n  return (\n    <div className=\"flex justify-center gap-4 py-8\">\n      {logoColumns.map((columnLogos, index) => (\n        <LogoColumn\n          key={index}\n          logos={columnLogos}\n          columnIndex={index}\n          currentTime={time}\n        />\n      ))}\n    </div>\n  );\n}\n\n// Column component\nfunction LogoColumn({ logos, columnIndex, currentTime }: LogoColumnProps) {\n  const CYCLE_DURATION = 2000;\n  const columnDelay = columnIndex * 200;\n  const adjustedTime =\n    (currentTime + columnDelay) % (CYCLE_DURATION * logos.length);\n  const currentIndex = Math.floor(adjustedTime / CYCLE_DURATION);\n  const currentLogo = logos[currentIndex];\n\n  return (\n    <motion.div\n      className=\"relative h-14 w-24 overflow-hidden md:h-24 md:w-48\"\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{\n        delay: columnIndex * 0.1,\n        duration: 0.5,\n        ease: \"easeOut\",\n      }}\n    >\n      <AnimatePresence mode=\"wait\">\n        <motion.div\n          key={`${currentLogo.id}-${currentIndex}`}\n          className=\"absolute inset-0 flex items-center justify-center\"\n          initial={{ y: \"10%\", opacity: 0 }}\n          animate={{\n            y: \"0%\",\n            opacity: 1,\n            transition: {\n              type: \"spring\",\n              stiffness: 300,\n              damping: 20,\n            },\n          }}\n          exit={{\n            y: \"-20%\",\n            opacity: 0,\n            transition: { duration: 0.3 },\n          }}\n        >\n          <Image\n            src={currentLogo.src}\n            alt={currentLogo.name}\n            width={120}\n            height={40}\n            className=\"h-auto w-auto max-h-[80%] max-w-[80%] object-contain\"\n          />\n        </motion.div>\n      </AnimatePresence>\n    </motion.div>\n  );\n}", "files": [{"path": "components/prismui/logo-carousel.tsx", "type": "registry:ui"}], "dependencies": ["framer-motion"], "category": "components", "subcategory": "display"}, {"name": "floating-action-panel", "type": "registry:ui", "code": "\"use client\";\n\nimport * as React from \"react\";\nimport { AnimatePresence, MotionConfig, motion } from \"framer-motion\";\nimport { cn } from \"@/lib/utils\";\n\nconst TRANSITION = {\n  type: \"spring\",\n  bounce: 0.1,\n  duration: 0.4,\n};\n\ninterface FloatingActionPanelContextType {\n  isOpen: boolean;\n  openPanel: (rect: DOMRect, mode: \"actions\" | \"note\") => void;\n  closePanel: () => void;\n  uniqueId: string;\n  triggerRect: DOMRect | null;\n  title: string;\n  setTitle: (title: string) => void;\n  note: string;\n  setNote: (note: string) => void;\n  mode: \"actions\" | \"note\";\n}\n\nconst FloatingActionPanelContext = React.createContext<\n  FloatingActionPanelContextType | undefined\n>(undefined);\n\nfunction useFloatingActionPanelLogic() {\n  const uniqueId = React.useId();\n  const [isOpen, setIsOpen] = React.useState(false);\n  const [triggerRect, setTriggerRect] = React.useState<DOMRect | null>(null);\n  const [title, setTitle] = React.useState(\"\");\n  const [note, setNote] = React.useState(\"\");\n  const [mode, setMode] = React.useState<\"actions\" | \"note\">(\"actions\");\n\n  const openPanel = (rect: DOMRect, newMode: \"actions\" | \"note\") => {\n    setTriggerRect(rect);\n    setMode(newMode);\n    setIsOpen(true);\n  };\n  const closePanel = () => {\n    setIsOpen(false);\n    setNote(\"\");\n  };\n\n  return {\n    isOpen,\n    openPanel,\n    closePanel,\n    uniqueId,\n    triggerRect,\n    title,\n    setTitle,\n    note,\n    setNote,\n    mode,\n  };\n}\n\ninterface FloatingActionPanelRootProps {\n  children: (context: FloatingActionPanelContextType) => React.ReactNode;\n  className?: string;\n}\n\nexport function FloatingActionPanelRoot({\n  children,\n  className,\n}: FloatingActionPanelRootProps) {\n  const floatingPanelLogic = useFloatingActionPanelLogic();\n\n  return (\n    <FloatingActionPanelContext.Provider value={floatingPanelLogic}>\n      <MotionConfig transition={TRANSITION}>\n        <div className={cn(\"relative\", className)}>\n          {children(floatingPanelLogic)}\n        </div>\n      </MotionConfig>\n    </FloatingActionPanelContext.Provider>\n  );\n}\n\ninterface FloatingActionPanelTriggerProps {\n  children: React.ReactNode;\n  className?: string;\n  title: string;\n  mode: \"actions\" | \"note\";\n}\n\nexport function FloatingActionPanelTrigger({\n  children,\n  className,\n  title,\n  mode,\n}: FloatingActionPanelTriggerProps) {\n  const { openPanel, uniqueId, setTitle } = React.useContext(FloatingActionPanelContext)!;\n  const triggerRef = React.useRef<HTMLButtonElement>(null);\n\n  const handleClick = () => {\n    if (triggerRef.current) {\n      openPanel(triggerRef.current.getBoundingClientRect(), mode);\n      setTitle(title);\n    }\n  };\n\n  return (\n    <motion.button\n      ref={triggerRef}\n      layoutId={`floating-panel-trigger-${uniqueId}-${mode}`}\n      className={cn(\n        \"flex h-9 items-center rounded-md border border-zinc-200 bg-white px-3 text-sm font-medium text-zinc-900 shadow-sm hover:bg-zinc-50 dark:border-zinc-800 dark:bg-zinc-950 dark:text-zinc-50 dark:hover:bg-zinc-800\",\n        className\n      )}\n      onClick={handleClick}\n      whileHover={{ scale: 1.02 }}\n      whileTap={{ scale: 0.98 }}\n    >\n      {children}\n    </motion.button>\n  );\n}\n\ninterface FloatingActionPanelContentProps {\n  children?: React.ReactNode;\n  className?: string;\n}\n\nexport function FloatingActionPanelContent({\n  children,\n  className,\n}: FloatingActionPanelContentProps) {\n  const { isOpen, closePanel, uniqueId, triggerRect, title, mode } =\n    React.useContext(FloatingActionPanelContext)!;\n  const contentRef = React.useRef<HTMLDivElement>(null);\n\n  React.useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (\n        contentRef.current &&\n        !contentRef.current.contains(event.target as Node)\n      ) {\n        closePanel();\n      }\n    };\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => document.removeEventListener(\"mousedown\", handleClickOutside);\n  }, [closePanel]);\n\n  React.useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.key === \"Escape\") closePanel();\n    };\n    document.addEventListener(\"keydown\", handleKeyDown);\n    return () => document.removeEventListener(\"keydown\", handleKeyDown);\n  }, [closePanel]);\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <>\n          <motion.div\n            initial={{ backdropFilter: \"blur(0px)\" }}\n            animate={{ backdropFilter: \"blur(4px)\" }}\n            exit={{ backdropFilter: \"blur(0px)\" }}\n            className=\"fixed inset-0 z-40 bg-black/5\"\n          />\n          <motion.div\n            ref={contentRef}\n            layoutId={`floating-panel-${uniqueId}-${mode}`}\n            className={cn(\n              \"fixed z-50 min-w-[200px] overflow-hidden rounded-lg border border-zinc-200 bg-white shadow-lg outline-none dark:border-zinc-800 dark:bg-zinc-950\",\n              className\n            )}\n            style={{\n              left: triggerRect ? triggerRect.left : \"50%\",\n              top: triggerRect ? triggerRect.bottom + 8 : \"50%\",\n              transformOrigin: \"top left\",\n            }}\n            initial={{ opacity: 0, scale: 0.9, y: -8 }}\n            animate={{ opacity: 1, scale: 1, y: 0 }}\n            exit={{ opacity: 0, scale: 0.9, y: -8 }}\n          >\n            <div className=\"px-4 py-3 font-medium\">{title}</div>\n            {children}\n          </motion.div>\n        </>\n      )}\n    </AnimatePresence>\n  );\n}\n\ninterface FloatingActionPanelButtonProps {\n  children: React.ReactNode;\n  onClick?: () => void;\n  className?: string;\n}\n\nexport function FloatingActionPanelButton({\n  children,\n  onClick,\n  className,\n}: FloatingActionPanelButtonProps) {\n  return (\n    <motion.button\n      className={cn(\n        \"flex w-full items-center gap-2 rounded-md px-2 py-1.5 text-sm text-zinc-900 hover:bg-zinc-100 dark:text-zinc-50 dark:hover:bg-zinc-800\",\n        className\n      )}\n      onClick={onClick}\n      whileHover={{ backgroundColor: \"rgba(0, 0, 0, 0.05)\" }}\n      whileTap={{ scale: 0.98 }}\n    >\n      {children}\n    </motion.button>\n  );\n}\n\ninterface FloatingActionPanelFormProps {\n  children: React.ReactNode;\n  onSubmit?: (note: string) => void;\n  className?: string;\n}\n\nexport function FloatingActionPanelForm({\n  children,\n  onSubmit,\n  className,\n}: FloatingActionPanelFormProps) {\n  const { note, closePanel } = React.useContext(FloatingActionPanelContext)!;\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    onSubmit?.(note);\n    closePanel();\n  };\n\n  return (\n    <form\n      className={cn(\"flex h-full flex-col\", className)}\n      onSubmit={handleSubmit}\n    >\n      {children}\n    </form>\n  );\n}\n\ninterface FloatingActionPanelTextareaProps {\n  className?: string;\n  id?: string;\n}\n\nexport function FloatingActionPanelTextarea({\n  className,\n  id,\n}: FloatingActionPanelTextareaProps) {\n  const { note, setNote } = React.useContext(FloatingActionPanelContext)!;\n\n  return (\n    <textarea\n      id={id}\n      className={cn(\n        \"h-full w-full resize-none rounded-md bg-transparent px-4 py-3 text-sm outline-none\",\n        className\n      )}\n      autoFocus\n      value={note}\n      onChange={(e) => setNote(e.target.value)}\n    />\n  );\n}", "files": [{"path": "components/prismui/floating-action-panel.tsx", "type": "registry:ui"}], "dependencies": ["framer-motion", "lucide-react"], "category": "components", "subcategory": "overlay"}, {"name": "hero-badge", "type": "registry:ui", "code": "\"use client\";\n\nimport { motion, useAnimation, type Variants } from \"framer-motion\";\nimport Link from \"next/link\";\nimport { cn } from \"@/lib/utils\";\n\nconst ease = [0.16, 1, 0.3, 1];\n\ninterface HeroBadgeProps {\n  href?: string;\n  text: string;\n  icon?: React.ReactNode;\n  endIcon?: React.ReactNode;\n  variant?: \"default\" | \"outline\" | \"ghost\";\n  size?: \"sm\" | \"md\" | \"lg\";\n  className?: string;\n  onClick?: () => void;\n}\n\nconst badgeVariants: Record<string, string> = {\n  default: \"bg-background hover:bg-muted\",\n  outline: \"border-2 hover:bg-muted\",\n  ghost: \"hover:bg-muted/50\",\n};\n\nconst sizeVariants: Record<string, string> = {\n  sm: \"px-3 py-1 text-xs gap-1.5\",\n  md: \"px-4 py-1.5 text-sm gap-2\",\n  lg: \"px-5 py-2 text-base gap-2.5\",\n};\n\nconst iconAnimationVariants: Variants = {\n  initial: { rotate: 0 },\n  hover: { rotate: -10 },\n};\n\nexport default function HeroBadge({\n  href,\n  text,\n  icon,\n  endIcon,\n  variant = \"default\",\n  size = \"md\",\n  className,\n  onClick,\n}: HeroBadgeProps) {\n  const controls = useAnimation();\n\n  const BadgeWrapper = href ? Link : motion.button;\n  const wrapperProps = href ? { href } : { onClick };\n\n  const baseClassName = cn(\n    \"inline-flex items-center rounded-full border transition-colors\",\n    badgeVariants[variant],\n    sizeVariants[size],\n    className\n  );\n\n  return (\n    <BadgeWrapper\n      {...wrapperProps}\n      className={cn(\"group\", href && \"cursor-pointer\")}\n    >\n      <motion.div\n        className={baseClassName}\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.8, ease }}\n        onHoverStart={() => controls.start(\"hover\")}\n        onHoverEnd={() => controls.start(\"initial\")}\n      >\n        {icon && (\n          <motion.div\n            className=\"text-foreground/60 transition-colors group-hover:text-primary\"\n            variants={iconAnimationVariants}\n            initial=\"initial\"\n            animate={controls}\n            transition={{ type: \"spring\", stiffness: 300, damping: 10 }}\n          >\n            {icon}\n          </motion.div>\n        )}\n        <span>{text}</span>\n        {endIcon && (\n          <motion.div className=\"text-foreground/60\">{endIcon}</motion.div>\n        )}\n      </motion.div>\n    </BadgeWrapper>\n  );\n}", "files": [{"path": "components/prismui/hero-badge.tsx", "type": "registry:ui"}], "dependencies": ["framer-motion"], "category": "components", "subcategory": "display"}, {"name": "action-button", "type": "registry:ui", "code": "\"use client\";\n\nimport { LoaderCircle } from \"lucide-react\";\nimport { cn } from \"@/lib/utils\";\nimport { VariantProps } from \"class-variance-authority\";\nimport { But<PERSON> } from \"../ui/button\";\nimport { buttonVariants } from \"../ui/button\";\n\ninterface props\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  children: React.ReactNode;\n  isPending: boolean;\n  onClick?: () => void;\n}\n\nexport default function ActionButton({\n  children,\n  isPending,\n  variant,\n  size,\n  className,\n  onClick,\n}: props) {\n  return (\n    <Button\n      onClick={\n        onClick\n          ? (e: React.MouseEvent<HTMLButtonElement>) => {\n              e.preventDefault();\n              onClick();\n            }\n          : undefined\n      }\n      type=\"submit\"\n      disabled={isPending}\n      variant={variant}\n      size={size}\n      className={cn(\n        className,\n        \"inline-grid place-items-center [grid-template-areas:'stack']\"\n      )}\n    >\n      <span\n        className={cn(\n          isPending && \"invisible\",\n          \"flex items-center gap-2 [grid-area:stack]\"\n        )}\n      >\n        {children}\n      </span>\n      <LoaderCircle\n        aria-label=\"Submitting\"\n        className={cn(\n          isPending ? \"visible\" : \"invisible\",\n          \"size-5 animate-spin transition-opacity [grid-area:stack]\"\n        )}\n      />\n    </Button>\n  );\n}", "files": [{"path": "components/prismui/action-button.tsx", "type": "registry:ui"}], "dependencies": ["lucide-react", "class-variance-authority", "@/components/ui/button"], "category": "components", "subcategory": "form"}, {"name": "button-group", "type": "registry:ui", "code": "\"use client\";\n\nimport { cn } from \"@/lib/utils\";\nimport { cva, VariantProps } from \"class-variance-authority\";\nimport React from \"react\";\n\nconst buttonGroupVariants = cva(\n  \"flex sm:items-center max-sm:gap-1 max-sm:flex-col [&>*:focus-within]:ring-1 [&>*:focus-within]:z-10 [&>*]:ring-offset-0 sm:[&>*:not(:first-child)]:rounded-l-none sm:[&>*:not(:last-child)]:rounded-r-none\",\n  {\n    variants: {\n      size: {\n        default: \"[&>*]:h-10 [&>*]:px-4 [&>*]:py-2\",\n        sm: \"[&>*]:h-9 [&>*]:rounded-md [&>*]:px-3\",\n        lg: \"[&>*]:h-11 [&>*]:rounded-md [&>*]:px-8\",\n        icon: \"[&>*]:h-10 [&>*]:w-10\",\n      },\n      separated: {\n        true: \"[&>*]:outline [&>*]:outline-1 [&>*]:outline-zinc-500 gap-0.5 [&>*:focus-within]:ring-offset-2\",\n        false: \"[&>*:focus-within]:ring-offset-1\",\n      },\n    },\n    defaultVariants: {\n      separated: false,\n      size: \"default\",\n    },\n  }\n);\n\nexport interface ButtonGroupProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof buttonGroupVariants> {\n  separated?: boolean;\n}\n\nconst ButtonGroup = React.forwardRef<HTMLDivElement, ButtonGroupProps>(\n  ({ children, className, size, separated = false, ...props }, ref) => {\n    return (\n      <div\n        className={cn(buttonGroupVariants({ size, className, separated }))}\n        ref={ref}\n        {...props}\n      >\n        {children}\n      </div>\n    );\n  }\n);\nButtonGroup.displayName = \"ButtonGroup\";\n\nexport { ButtonGroup };", "files": [{"path": "components/prismui/button-group.tsx", "type": "registry:ui"}], "dependencies": ["class-variance-authority"], "category": "components", "subcategory": "form"}, {"name": "expandable-card", "type": "registry:ui", "code": "\"use client\";\n\nimport React, { useRef, useEffect } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport {\n  Clock,\n  GitBranch,\n  Github,\n  MessageSquare,\n  StepForwardIcon as Progress,\n  Star,\n  Users,\n  CheckCircle2,\n} from \"lucide-react\";\nimport {\n  <PERSON>,\n  CardContent,\n  <PERSON><PERSON>ooter,\n  CardHeader,\n} from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Button } from \"@/components/ui/button\";\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\nimport { Progress as ProgressBar } from \"@/components/ui/progress\";\nimport { useState, useCallback } from \"react\";\nimport { useSpring } from \"framer-motion\";\nimport {\n  Tooltip,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from \"@/components/ui/tooltip\";\n\n\ninterface ProjectStatusCardProps {\n  title: string;\n  progress: number;\n  dueDate: string;\n  contributors: Array<{ name: string; image?: string }>;\n  tasks: Array<{ title: string; completed: boolean }>;\n  githubStars: number;\n  openIssues: number;\n}\n\nexport function useExpandable(initialState = false) {\n  const [isExpanded, setIsExpanded] = useState(initialState);\n\n  const springConfig = { stiffness: 300, damping: 30 };\n  const animatedHeight = useSpring(0, springConfig);\n\n  const toggleExpand = useCallback(() => {\n    setIsExpanded((prev) => !prev);\n  }, []);\n\n  return { isExpanded, toggleExpand, animatedHeight };\n}\n\n\nexport function ProjectStatusCard({\n  title,\n  progress,\n  dueDate,\n  contributors,\n  tasks,\n  githubStars,\n  openIssues,\n}: ProjectStatusCardProps) {\n  const { isExpanded, toggleExpand, animatedHeight } = useExpandable();\n  const contentRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    if (contentRef.current) {\n      animatedHeight.set(isExpanded ? contentRef.current.scrollHeight : 0);\n    }\n  }, [isExpanded, animatedHeight]);\n\n  return (\n    <Card\n      className=\"w-full max-w-md cursor-pointer transition-all duration-300 hover:shadow-lg\"\n      onClick={toggleExpand}\n    >\n      <CardHeader className=\"space-y-1\">\n        <div className=\"flex justify-between items-start w-full\">\n          <div className=\"space-y-2\">\n            <Badge\n              variant=\"secondary\"\n              className={\n                progress === 100\n                  ? \"bg-green-100 text-green-600\"\n                  : \"bg-blue-100 text-blue-600\"\n              }\n            >\n              {progress === 100 ? \"Completed\" : \"In Progress\"}\n            </Badge>\n            <h3 className=\"text-2xl font-semibold\">{title}</h3>\n          </div>\n          <TooltipProvider>\n            <Tooltip>\n              <TooltipTrigger asChild>\n                <Button size=\"icon\" variant=\"outline\" className=\"h-8 w-8\">\n                  <Github className=\"h-4 w-4\" />\n                </Button>\n              </TooltipTrigger>\n              <TooltipContent>\n                <p>View on GitHub</p>\n              </TooltipContent>\n            </Tooltip>\n          </TooltipProvider>\n        </div>\n      </CardHeader>\n\n      <CardContent>\n        <div className=\"space-y-4\">\n          <div className=\"space-y-2\">\n            <div className=\"flex justify-between text-sm text-gray-600\">\n              <span>Progress</span>\n              <span>{progress}%</span>\n            </div>\n            <ProgressBar value={progress} className=\"h-2\" />\n          </div>\n\n          <motion.div\n            style={{ height: animatedHeight }}\n            transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\n            className=\"overflow-hidden\"\n          >\n            <div ref={contentRef}>\n              <AnimatePresence>\n                {isExpanded && (\n                  <motion.div\n                    initial={{ opacity: 0 }}\n                    animate={{ opacity: 1 }}\n                    exit={{ opacity: 0 }}\n                    className=\"space-y-4 pt-2\"\n                  >\n                    <div className=\"flex items-center justify-between text-sm text-gray-600\">\n                      <div className=\"flex items-center\">\n                        <Clock className=\"h-4 w-4 mr-2\" />\n                        <span>Due {dueDate}</span>\n                      </div>\n                      <div className=\"flex items-center gap-4\">\n                        <div className=\"flex items-center\">\n                          <Star className=\"h-4 w-4 mr-1 text-yellow-400\" />\n                          <span>{githubStars}</span>\n                        </div>\n                        <div className=\"flex items-center\">\n                          <GitBranch className=\"h-4 w-4 mr-1\" />\n                          <span>{openIssues} issues</span>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"space-y-2\">\n                      <h4 className=\"font-medium text-sm flex items-center\">\n                        <Users className=\"h-4 w-4 mr-2\" />\n                        Contributors\n                      </h4>\n                      <div className=\"flex -space-x-2\">\n                        {contributors.map((contributor, index) => (\n                          <TooltipProvider key={index}>\n                            <Tooltip>\n                              <TooltipTrigger asChild>\n                                <Avatar className=\"border-2 border-white\">\n                                  <AvatarImage\n                                    src={\n                                      contributor.image ||\n                                      `/placeholder.svg?height=32&width=32&text=${contributor.name[0]}`\n                                    }\n                                    alt={contributor.name}\n                                  />\n                                  <AvatarFallback>\n                                    {contributor.name[0]}\n                                  </AvatarFallback>\n                                </Avatar>\n                              </TooltipTrigger>\n                              <TooltipContent>\n                                <p>{contributor.name}</p>\n                              </TooltipContent>\n                            </Tooltip>\n                          </TooltipProvider>\n                        ))}\n                      </div>\n                    </div>\n\n                    <div className=\"space-y-2\">\n                      <h4 className=\"font-medium text-sm\">Recent Tasks</h4>\n                      {tasks.map((task, index) => (\n                        <div\n                          key={index}\n                          className=\"flex items-center justify-between text-sm\"\n                        >\n                          <span className=\"text-gray-600\">{task.title}</span>\n                          {task.completed && (\n                            <CheckCircle2 className=\"h-4 w-4 text-green-500\" />\n                          )}\n                        </div>\n                      ))}\n                    </div>\n\n                    <div className=\"space-y-2\">\n                      <Button className=\"w-full\">\n                        <MessageSquare className=\"h-4 w-4 mr-2\" />\n                        View Discussion\n                      </Button>\n                    </div>\n                  </motion.div>\n                )}\n              </AnimatePresence>\n            </div>\n          </motion.div>\n        </div>\n      </CardContent>\n\n      <CardFooter>\n        <div className=\"flex items-center justify-between w-full text-sm text-gray-600\">\n          <span>Last updated: 2 hours ago</span>\n          <span>{openIssues} open issues</span>\n        </div>\n      </CardFooter>\n    </Card>\n  );\n}", "files": [{"path": "components/prismui/expandable-card.tsx", "type": "registry:ui"}], "dependencies": ["framer-motion", "lucide-react"], "category": "components", "subcategory": "display"}, {"name": "display-cards", "type": "registry:ui", "code": "\"use client\";\n\nimport { cn } from \"@/lib/utils\";\nimport { AudioLines } from \"lucide-react\";\n\ninterface DisplayCardProps {\n  className?: string;\n  icon?: React.ReactNode;\n  title?: string;\n  description?: string;\n  date?: string;\n  iconClassName?: string;\n  titleClassName?: string;\n}\n\nfunction DisplayCard({\n  className,\n  icon = <AudioLines className=\"size-4 text-green-300\" />,\n  title = \"Featured\",\n  description = \"This is a skewed card with some text\",\n  date = \"Sep 23\",\n  iconClassName = \"text-green-500\",\n  titleClassName = \"text-green-500\",\n}: DisplayCardProps) {\n  return (\n    <div\n      className={cn(\n        \"relative flex h-36 w-[22rem] -skew-y-[8deg] select-none flex-col justify-between rounded-xl border-2 bg-muted/70 backdrop-blur-sm px-4 py-3 transition-all duration-700 after:absolute after:-right-1 after:top-[-5%] after:h-[110%] after:w-[20rem] after:bg-gradient-to-l after:from-background after:to-transparent after:content-[''] hover:border-white/20 hover:bg-muted [&>*]:flex [&>*]:items-center [&>*]:gap-2\",\n        className\n      )}\n    >\n      <div>\n        <span className=\"relative inline-block rounded-full bg-green-800 p-1\">\n          {icon}\n        </span>\n        <p className={cn(\"text-lg\", titleClassName)}>{title}</p>\n      </div>\n      <p className=\"whitespace-nowrap text-lg\">{description}</p>\n      <p className=\"text-muted-foreground\">{date}</p>\n    </div>\n  );\n}\n\ninterface DisplayCardsProps {\n  cards?: DisplayCardProps[];\n}\n\nexport default function DisplayCards({ cards }: DisplayCardsProps) {\n  const defaultCards = [\n    {\n      className: \"[grid-area:stack] hover:-translate-y-10 before:absolute before:w-[100%] before:outline-1 before:rounded-xl before:outline-border before:h-[100%] before:content-[''] before:bg-blend-overlay before:bg-background/50 grayscale-[100%] hover:before:opacity-0 before:transition-opacity before:duration:700 hover:grayscale-0 before:left-0 before:top-0\",\n    },\n    {\n      className: \"[grid-area:stack] translate-x-16 translate-y-10 hover:-translate-y-10 before:absolute before:w-[100%] before:outline-1 before:rounded-xl before:outline-border before:h-[100%] before:content-[''] before:bg-blend-overlay before:bg-background/50 grayscale-[100%] hover:before:opacity-0 before:transition-opacity before:duration:700 hover:grayscale-0 before:left-0 before:top-0\",\n    },\n    {\n      className: \"[grid-area:stack] translate-x-32 translate-y-20 hover:translate-y-10\",\n    },\n  ];\n\n  const displayCards = cards || defaultCards;\n\n  return (\n    <div className=\"grid [grid-template-areas:'stack'] place-items-center opacity-100 animate-in fade-in-0 duration-700\">\n      {displayCards.map((cardProps, index) => (\n        <DisplayCard key={index} {...cardProps} />\n      ))}\n    </div>\n  );\n}", "files": [{"path": "components/prismui/display-cards.tsx", "type": "registry:ui"}], "dependencies": ["lucide-react"], "category": "components", "subcategory": "display"}, {"name": "hero", "type": "registry:ui", "code": "\"use client\";\n\nimport { motion, useAnimation } from \"framer-motion\";\nimport { Icons } from \"@/components/icons\";\nimport { buttonVariants } from \"@/components/ui/button\";\nimport { cn } from \"@/lib/utils\";\nimport Link from \"next/link\";\n\nconst ease = [0.16, 1, 0.3, 1];\n\ninterface HeroPillProps {\n  href?: string;\n  text: string;\n  icon?: React.ReactNode;\n  endIcon?: React.ReactNode;\n}\n\nfunction HeroPill({ href, text, icon, endIcon }: HeroPillProps) {\n  const controls = useAnimation();\n\n  return (\n    <Link href={href || \"/docs\"} className=\"group\">\n      <motion.div\n        className=\"inline-flex items-center gap-2 rounded-full border bg-background px-4 py-1.5 text-sm transition-colors hover:bg-muted\"\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.8, ease }}\n        onHoverStart={() => controls.start({ rotate: -10 })}\n        onHoverEnd={() => controls.start({ rotate: 0 })}\n      >\n        <motion.div\n          className=\"text-foreground/60 transition-colors group-hover:text-primary\"\n          animate={controls}\n          transition={{ type: \"spring\", stiffness: 300, damping: 10 }}\n        >\n          {icon || <Icons.logo className=\"h-4 w-4\" />}\n        </motion.div>\n        <span>{text}</span>\n        {endIcon || <Icons.chevronRight className=\"h-4 w-4\" />}\n      </motion.div>\n    </Link>\n  );\n}\n\ninterface HeroContentProps {\n  title: string;\n  titleHighlight?: string;\n  description: string;\n  primaryAction?: {\n    href: string;\n    text: string;\n    icon?: React.ReactNode;\n  };\n  secondaryAction?: {\n    href: string;\n    text: string;\n    icon?: React.ReactNode;\n  };\n}\n\nfunction HeroContent({\n  title,\n  titleHighlight,\n  description,\n  primaryAction,\n  secondaryAction,\n}: HeroContentProps) {\n  return (\n    <div className=\"flex flex-col space-y-4\">\n      <motion.h1\n        className=\"text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl xl:text-8xl\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.8, ease }}\n      >\n        {title}{\" \"}\n        {titleHighlight && <span className=\"text-primary\">{titleHighlight}</span>}\n      </motion.h1>\n      <motion.p\n        className=\"max-w-[42rem] leading-normal text-muted-foreground sm:text-xl sm:leading-8\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.1, duration: 0.8, ease }}\n      >\n        {description}\n      </motion.p>\n      <motion.div\n        className=\"flex flex-col sm:flex-row gap-4 pt-4\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.2, duration: 0.8, ease }}\n      >\n        {primaryAction && (\n          <Link\n            href={primaryAction.href}\n            className={cn(\n              buttonVariants({ size: \"lg\" }),\n              \"gap-2 w-full sm:w-auto justify-center\"\n            )}\n          >\n            {primaryAction.icon}\n            {primaryAction.text}\n          </Link>\n        )}\n        {secondaryAction && (\n          <Link\n            href={secondaryAction.href}\n            className={cn(\n              buttonVariants({ variant: \"outline\", size: \"lg\" }),\n              \"gap-2 w-full sm:w-auto justify-center\"\n            )}\n          >\n            {secondaryAction.icon}\n            {secondaryAction.text}\n          </Link>\n        )}\n      </motion.div>\n    </div>\n  );\n}\n\ninterface HeroProps {\n  pill?: {\n    href?: string;\n    text: string;\n    icon?: React.ReactNode;\n    endIcon?: React.ReactNode;\n  };\n  content: HeroContentProps;\n  preview?: React.ReactNode;\n  className?: string;\n}\n\nexport default function Hero({ pill, content, preview, className }: HeroProps) {\n  return (\n    <div className={cn(\"container relative overflow-hidden\", className)}>\n      <div className=\"flex min-h-[calc(100vh-64px)] flex-col lg:flex-row items-center py-8 px-4 md:px-8 lg:px-12\">\n        <div className=\"flex flex-col gap-4 w-full lg:max-w-2xl\">\n          {pill && <HeroPill {...pill} />}\n          <HeroContent {...content} />\n        </div>\n        {preview && (\n          <div className=\"w-full lg:max-w-xl lg:pl-16 mt-12 lg:mt-0\">\n            {preview}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}", "files": [{"path": "components/prismui/hero.tsx", "type": "registry:ui"}], "dependencies": ["framer-motion"], "category": "components", "subcategory": "marketing"}, {"name": "open-source", "type": "registry:ui", "code": "\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Separator } from \"@/components/ui/separator\";\nimport Image from \"next/image\";\nimport { cn } from \"@/lib/utils\";\nimport { Suspense } from \"react\";\n\ninterface Contributor {\n  avatar_url: string;\n  login: string;\n}\n\ninterface Stats {\n  stars: number;\n  contributors: Contributor[];\n}\n\ninterface OpenSourceProps {\n  /** The repository owner/name (e.g., \"codehagen/prismui\") */\n  repository: string;\n  /** Optional GitHub OAuth token for API requests */\n  githubToken?: string;\n  /** Optional default stats to show while loading */\n  defaultStats?: Stats;\n  /** Optional custom title */\n  title?: string;\n  /** Optional custom description */\n  description?: string;\n  /** Optional custom button text */\n  buttonText?: string;\n  /** Optional className for styling */\n  className?: string;\n}\n\nasync function getGithubStats(repository: string, githubToken?: string): Promise<Stats> {\n  try {\n    const [repoResponse, contributorsResponse] = await Promise.all([\n      fetch(`https://api.github.com/repos/${repository}`, {\n        ...(githubToken && {\n          headers: {\n            Authorization: `Bearer ${githubToken}`,\n            \"Content-Type\": \"application/json\",\n          },\n        }),\n        next: { revalidate: 3600 },\n      }),\n      fetch(`https://api.github.com/repos/${repository}/contributors`, {\n        ...(githubToken && {\n          headers: {\n            Authorization: `Bearer ${githubToken}`,\n            \"Content-Type\": \"application/json\",\n          },\n        }),\n        next: { revalidate: 3600 },\n      }),\n    ]);\n\n    if (!repoResponse.ok || !contributorsResponse.ok) {\n      return { stars: 0, contributors: [] };\n    }\n\n    const repoData = await repoResponse.json();\n    const contributorsData = await contributorsResponse.json();\n\n    return {\n      stars: repoData.stargazers_count,\n      contributors: contributorsData as Contributor[],\n    };\n  } catch (error) {\n    return { stars: 0, contributors: [] };\n  }\n}\n\nfunction StarIcon({\n  className,\n  delay = 0,\n  size = \"default\",\n}: {\n  className?: string;\n  delay?: number;\n  size?: \"small\" | \"default\";\n}) {\n  return (\n    <motion.div\n      initial={{ opacity: 0, scale: 0 }}\n      animate={{ opacity: 1, scale: 1 }}\n      whileHover={{ scale: 1.2, rotate: 20 }}\n      transition={{\n        duration: 0.8,\n        delay,\n        ease: [0.16, 1, 0.3, 1],\n        hover: {\n          duration: 0.2,\n          ease: \"easeOut\",\n        },\n      }}\n      className={className}\n    >\n      <svg\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n        className={cn(\n          \"text-yellow-400\",\n          size === \"small\" ? \"w-4 h-4\" : \"w-8 h-8\"\n        )}\n      >\n        <path\n          d=\"M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z\"\n          fill=\"currentColor\"\n          stroke=\"currentColor\"\n          strokeWidth=\"2\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n          className={cn(size === \"small\" && \"opacity-20\")}\n        />\n      </svg>\n    </motion.div>\n  );\n}\n\nfunction StarsDecoration() {\n  return (\n    <div className=\"absolute -top-8 left-1/2 -translate-x-1/2\">\n      <div className=\"flex gap-4\">\n        <StarIcon delay={0.2} />\n        <StarIcon delay={0.3} />\n        <StarIcon delay={0.4} />\n      </div>\n    </div>\n  );\n}\n\nfunction ContributorAvatars({ contributors }: { contributors: Contributor[] }) {\n  const displayedContributors = contributors.slice(0, 8);\n\n  return (\n    <div className=\"flex flex-wrap gap-2\">\n      {displayedContributors.map((contributor) => (\n        <motion.div\n          key={contributor.login}\n          whileHover={{ scale: 1.1, y: -3 }}\n          transition={{ type: \"spring\", stiffness: 400, damping: 17 }}\n        >\n          <Image\n            src={contributor.avatar_url}\n            alt={`${contributor.login}'s avatar`}\n            width={40}\n            height={40}\n            className=\"rounded-full border-2 border-background\"\n          />\n        </motion.div>\n      ))}\n    </div>\n  );\n}\n\nfunction OpenSourceCard({\n  repository,\n  stars,\n  contributors,\n}: {\n  repository: string;\n  stars: number;\n  contributors: Contributor[];\n}) {\n  return (\n    <div className=\"relative grid md:grid-cols-2 gap-8 items-center\">\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        whileInView={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.8, ease: [0.16, 1, 0.3, 1] }}\n        viewport={{ once: true }}\n        className=\"relative flex flex-col items-center text-center\"\n      >\n        <motion.a\n          href={`https://github.com/${repository}`}\n          target=\"_blank\"\n          rel=\"noreferrer\"\n          className=\"relative inline-flex flex-col items-center cursor-pointer\"\n          whileHover={{ scale: 1.05 }}\n          transition={{ duration: 0.2 }}\n        >\n          <StarsDecoration />\n          <div className=\"flex flex-col items-center mt-2\">\n            <div className=\"text-7xl font-bold\">{stars}</div>\n            <div className=\"text-xl text-muted-foreground mt-2\">\n              Github Stars\n            </div>\n          </div>\n        </motion.a>\n      </motion.div>\n\n      <Separator className=\"md:hidden\" />\n\n      <div className=\"hidden md:block absolute left-1/2 top-0 h-full\">\n        <Separator orientation=\"vertical\" />\n      </div>\n\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        whileInView={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.8, ease: [0.16, 1, 0.3, 1] }}\n        viewport={{ once: true }}\n        className=\"text-center\"\n      >\n        <div className=\"space-y-4\">\n          <div>\n            <div className=\"text-3xl font-bold\">\n              {contributors.length}+ Contributors\n            </div>\n            <div className=\"text-lg text-muted-foreground mt-2\">\n              Join our growing community\n            </div>\n          </div>\n          <a\n            href={`https://github.com/${repository}/graphs/contributors`}\n            target=\"_blank\"\n            rel=\"noreferrer\"\n            className=\"inline-block\"\n          >\n            <div className=\"flex justify-center\">\n              <ContributorAvatars contributors={contributors} />\n            </div>\n          </a>\n        </div>\n      </motion.div>\n    </div>\n  );\n}\n\nfunction OpenSourceContent({\n  repository,\n  stars,\n  contributors,\n  title = \"Proudly open-source\",\n  description = \"Our source code is available on GitHub - feel free to read, review, or contribute to it however you want!\",\n  buttonText = \"Star on GitHub\",\n}: Stats & {\n  repository: string;\n  title?: string;\n  description?: string;\n  buttonText?: string;\n}) {\n  return (\n    <section className=\"container relative py-20\">\n      <div className=\"text-center mb-16\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, ease: [0.16, 1, 0.3, 1] }}\n          viewport={{ once: true }}\n        >\n          <h2 className=\"text-4xl font-bold tracking-tight sm:text-6xl mb-4\">\n            {title}\n          </h2>\n          <p className=\"text-xl text-muted-foreground max-w-[800px] mx-auto\">\n            {description}\n          </p>\n          <div className=\"mt-6\">\n            <Button variant=\"outline\" size=\"lg\" className=\"gap-2\" asChild>\n              <a\n                href={`https://github.com/${repository}`}\n                target=\"_blank\"\n                rel=\"noreferrer\"\n              >\n                <svg viewBox=\"0 0 438.549 438.549\" className=\"h-5 w-5\">\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M409.132 114.573c-19.608-33.596-46.205-60.194-79.798-79.8-33.598-19.607-70.277-29.408-110.063-29.408-39.781 0-76.472 9.804-110.063 29.408-33.596 19.605-60.192 46.204-79.8 79.8C9.803 148.168 0 184.854 0 224.63c0 47.78 13.94 90.745 41.827 128.906 27.884 38.164 63.906 64.572 108.063 79.227 5.14.954 8.945.283 11.419-1.996 2.475-2.282 3.711-5.14 3.711-8.562 0-.571-.049-5.708-.144-15.417a2549.81 2549.81 0 01-.144-25.406l-6.567 1.136c-4.187.767-9.469 1.092-15.846 1-6.374-.089-12.991-.757-19.842-1.999-6.854-1.231-13.229-4.086-19.13-8.559-5.898-4.473-10.085-10.328-12.56-17.556l-2.855-6.57c-1.903-4.374-4.899-9.233-8.992-14.559-4.093-5.331-8.232-8.945-12.419-10.848l-1.999-1.431c-1.332-.951-2.568-2.098-3.711-3.429-1.142-1.331-1.997-2.663-2.568-3.997-.572-1.335-.098-2.43 1.427-3.289 1.525-.859 4.281-1.276 8.28-1.276l5.708.853c3.807.763 8.516 3.042 14.133 6.851 5.614 3.806 10.229 8.754 13.846 14.842 4.38 7.806 9.657 13.754 15.846 17.847 6.184 4.093 12.419 6.136 18.699 6.136 6.28 0 11.704-.476 16.274-1.423 4.565-.952 8.848-2.383 12.847-4.285 1.713-12.758 6.377-22.559 13.988-29.41-10.848-1.14-20.601-2.857-29.264-5.14-8.658-2.286-17.605-5.996-26.835-11.14-9.235-5.137-16.896-11.516-22.985-19.126-6.09-7.614-11.088-17.61-14.987-29.979-3.901-12.374-5.852-26.648-5.852-42.826 0-23.035 7.52-42.637 22.557-58.817-7.044-17.318-6.379-36.732 1.997-58.24 5.52-1.715 13.706-.428 24.554 3.853 10.85 4.283 18.794 7.952 23.84 10.994 5.046 3.041 9.089 5.618 12.135 7.708 17.705-4.947 35.976-7.421 54.818-7.421s37.117 2.474 54.823 7.421l10.849-6.849c7.419-4.57 16.18-8.758 26.262-12.565 10.088-3.805 17.802-4.853 23.134-3.138 8.562 21.509 9.325 40.922 2.279 58.24 15.036 16.18 22.559 35.787 22.559 58.817 0 16.178-1.958 30.497-5.853 42.966-3.9 12.471-8.941 22.457-15.125 29.979-6.191 7.521-13.901 13.85-23.131 18.986-9.232 5.14-18.182 8.85-26.84 11.136-8.662 2.286-18.415 4.004-29.263 5.146 9.894 8.562 14.842 22.077 14.842 40.539v60.237c0 3.422 1.19 6.279 3.572 8.562 2.379 2.279 6.136 2.95 11.276 1.995 44.163-14.653 80.185-41.062 108.068-79.226 27.88-38.161 41.825-81.126 41.825-128.906-.01-39.771-9.818-76.454-29.414-110.049z\"\n                  ></path>\n                </svg>\n                {buttonText}\n              </a>\n            </Button>\n          </div>\n        </motion.div>\n      </div>\n      <Separator className=\"mb-16\" />\n      <div className=\"max-w-4xl mx-auto\">\n        <OpenSourceCard\n          repository={repository}\n          stars={stars}\n          contributors={contributors}\n        />\n      </div>\n    </section>\n  );\n}\n\nasync function OpenSourceData({\n  repository,\n  githubToken,\n  defaultStats = { stars: 0, contributors: [] },\n  ...props\n}: OpenSourceProps) {\n  const stats = await getGithubStats(repository, githubToken);\n  return <OpenSourceContent {...stats} {...props} />;\n}\n\nexport default function OpenSource(props: OpenSourceProps) {\n  return (\n    <Suspense\n      fallback={\n        <OpenSourceContent\n          stars={props.defaultStats?.stars || 0}\n          contributors={props.defaultStats?.contributors || []}\n          {...props}\n        />\n      }\n    >\n      <OpenSourceData {...props} />\n    </Suspense>\n  );\n}", "files": [{"path": "components/prismui/open-source.tsx", "type": "registry:ui"}], "dependencies": ["framer-motion"], "category": "components", "subcategory": "marketing"}, {"name": "number-flow", "type": "registry:ui", "code": "\"use client\";\n\nimport NumberF<PERSON> from \"@number-flow/react\";\nimport { type Format } from \"@number-flow/react\";\n\ninterface NumberFlowProps {\n  value: number;\n  format?: Format;\n  locales?: string | string[];\n  prefix?: string;\n  suffix?: string;\n  spinTiming?: EffectTiming;\n  willChange?: boolean;\n  continuous?: boolean;\n}\n\nexport default function NumberFlowWrapper({\n  value,\n  format = {},\n  locales,\n  prefix,\n  suffix,\n  spinTiming,\n  willChange = false,\n  continuous = false,\n}: NumberFlowProps) {\n  return (\n    <NumberFlow\n      value={value}\n      format={format}\n      locales={locales}\n      prefix={prefix}\n      suffix={suffix}\n      spinTiming={spinTiming}\n      willChange={willChange}\n      continuous={continuous}\n    />\n  );\n}", "files": [{"path": "components/prismui/number-flow.tsx", "type": "registry:ui"}], "dependencies": ["@number-flow/react"], "category": "components", "subcategory": "animation"}, {"name": "popover", "type": "registry:ui", "code": "\"use client\";\n\nimport * as React from \"react\";\nimport { AnimatePresence, MotionConfig, motion } from \"framer-motion\";\nimport { cn } from \"@/lib/utils\";\nimport { Button } from \"@/components/ui/button\";\n\nconst TRANSITION = {\n  type: \"spring\",\n  bounce: 0.1,\n  duration: 0.4,\n};\n\ninterface PopoverContextType {\n  isOpen: boolean;\n  openPopover: () => void;\n  closePopover: () => void;\n  uniqueId: string;\n  note: string;\n  setNote: (note: string) => void;\n}\n\nconst PopoverContext = React.createContext<PopoverContextType | undefined>(\n  undefined\n);\n\nfunction usePopoverLogic() {\n  const uniqueId = React.useId();\n  const [isOpen, setIsOpen] = React.useState(false);\n  const [note, setNote] = React.useState(\"\");\n\n  const openPopover = () => setIsOpen(true);\n  const closePopover = () => {\n    setIsOpen(false);\n    setNote(\"\");\n  };\n\n  return {\n    isOpen,\n    openPopover,\n    closePopover,\n    uniqueId,\n    note,\n    setNote,\n  };\n}\n\ninterface PopoverRootProps {\n  children: React.ReactNode;\n}\n\nexport function PopoverRoot({ children }: PopoverRootProps) {\n  const popoverLogic = usePopoverLogic();\n\n  return (\n    <PopoverContext.Provider value={popoverLogic}>\n      <MotionConfig transition={TRANSITION}>\n        <div className=\"relative\">{children}</div>\n      </MotionConfig>\n    </PopoverContext.Provider>\n  );\n}\n\ninterface PopoverTriggerProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: \"default\" | \"outline\" | \"secondary\" | \"ghost\";\n}\n\nexport function PopoverTrigger({\n  children,\n  className,\n  variant = \"default\",\n  ...props\n}: PopoverTriggerProps) {\n  const { openPopover, uniqueId } = React.useContext(PopoverContext)!;\n\n  return (\n    <motion.div layoutId={`popover-trigger-${uniqueId}`}>\n      <Button\n        variant={variant}\n        className={className}\n        onClick={openPopover}\n        {...props}\n      >\n        {children}\n      </Button>\n    </motion.div>\n  );\n}\n\ninterface PopoverContentProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport function PopoverContent({\n  children,\n  className,\n}: PopoverContentProps) {\n  const { isOpen, closePopover, uniqueId } = React.useContext(PopoverContext)!;\n  const contentRef = React.useRef<HTMLDivElement>(null);\n\n  React.useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (\n        contentRef.current &&\n        !contentRef.current.contains(event.target as Node)\n      ) {\n        closePopover();\n      }\n    };\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => document.removeEventListener(\"mousedown\", handleClickOutside);\n  }, [closePopover]);\n\n  React.useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.key === \"Escape\") closePopover();\n    };\n    document.addEventListener(\"keydown\", handleKeyDown);\n    return () => document.removeEventListener(\"keydown\", handleKeyDown);\n  }, [closePopover]);\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <>\n          <motion.div\n            initial={{ backdropFilter: \"blur(0px)\" }}\n            animate={{ backdropFilter: \"blur(4px)\" }}\n            exit={{ backdropFilter: \"blur(0px)\" }}\n            className=\"fixed inset-0 z-40 bg-black/5\"\n          />\n          <motion.div\n            ref={contentRef}\n            layoutId={`popover-${uniqueId}`}\n            className={cn(\n              \"fixed z-50 min-w-[200px] overflow-hidden rounded-lg border border-border bg-background shadow-lg outline-none\",\n              className\n            )}\n            style={{\n              left: \"50%\",\n              top: \"50%\",\n              transform: \"translate(-50%, -50%)\",\n            }}\n            initial={{ opacity: 0, scale: 0.9, y: -8 }}\n            animate={{ opacity: 1, scale: 1, y: 0 }}\n            exit={{ opacity: 0, scale: 0.9, y: -8 }}\n          >\n            {children}\n          </motion.div>\n        </>\n      )}\n    </AnimatePresence>\n  );\n}\n\ninterface PopoverFormProps {\n  children: React.ReactNode;\n  onSubmit?: (note: string) => void;\n}\n\nexport function PopoverForm({ children, onSubmit }: PopoverFormProps) {\n  const { note, closePopover } = React.useContext(PopoverContext)!;\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    onSubmit?.(note);\n    closePopover();\n  };\n\n  return (\n    <form className=\"flex h-full flex-col\" onSubmit={handleSubmit}>\n      {children}\n    </form>\n  );\n}\n\ninterface PopoverLabelProps {\n  children: React.ReactNode;\n}\n\nexport function PopoverLabel({ children }: PopoverLabelProps) {\n  return <div className=\"px-4 py-3 font-medium\">{children}</div>;\n}\n\ninterface PopoverTextareaProps {\n  className?: string;\n  id?: string;\n}\n\nexport function PopoverTextarea({ className, id }: PopoverTextareaProps) {\n  const { note, setNote } = React.useContext(PopoverContext)!;\n\n  return (\n    <textarea\n      id={id}\n      className={cn(\n        \"h-full w-full resize-none rounded-md bg-transparent px-4 py-3 text-sm outline-none\",\n        className\n      )}\n      autoFocus\n      value={note}\n      onChange={(e) => setNote(e.target.value)}\n    />\n  );\n}\n\ninterface PopoverFooterProps {\n  children: React.ReactNode;\n}\n\nexport function PopoverFooter({ children }: PopoverFooterProps) {\n  return (\n    <div className=\"flex items-center justify-between gap-2 border-t p-3\">\n      {children}\n    </div>\n  );\n}\n\nexport function PopoverCloseButton() {\n  const { closePopover } = React.useContext(PopoverContext)!;\n\n  return (\n    <Button variant=\"ghost\" size=\"sm\" onClick={closePopover}>\n      Cancel\n    </Button>\n  );\n}\n\ninterface PopoverSubmitButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: \"default\" | \"outline\" | \"secondary\" | \"ghost\";\n}\n\nexport function PopoverSubmitButton({\n  children,\n  variant = \"default\",\n  ...props\n}: PopoverSubmitButtonProps) {\n  return (\n    <Button type=\"submit\" variant={variant} size=\"sm\" {...props}>\n      {children}\n    </Button>\n  );\n}\n\ninterface PopoverHeaderProps {\n  children: React.ReactNode;\n}\n\nexport function PopoverHeader({ children }: PopoverHeaderProps) {\n  return <div className=\"px-4 py-3\">{children}</div>;\n}\n\ninterface PopoverBodyProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport function PopoverBody({ children, className }: PopoverBodyProps) {\n  return <div className={cn(\"px-2 py-1.5\", className)}>{children}</div>;\n}\n\ninterface PopoverButtonProps {\n  children: React.ReactNode;\n  onClick?: () => void;\n  className?: string;\n}\n\nexport function PopoverButton({\n  children,\n  onClick,\n  className,\n}: PopoverButtonProps) {\n  return (\n    <button\n      className={cn(\n        \"flex w-full items-center gap-2 rounded-md px-2 py-1.5 text-sm text-foreground hover:bg-muted\",\n        className\n      )}\n      onClick={onClick}\n    >\n      {children}\n    </button>\n  );\n}", "files": [{"path": "components/prismui/popover.tsx", "type": "registry:ui"}], "dependencies": ["framer-motion"], "category": "components", "subcategory": "overlay"}, {"name": "pricing", "type": "registry:ui", "code": "\"use client\";\n\n// ... (paste the entire component code here)\n", "files": [{"path": "components/prismui/pricing.tsx", "type": "registry:ui"}], "dependencies": ["framer-motion", "canvas-confetti"], "category": "sections", "subcategory": "marketing"}, {"name": "timeline", "type": "registry:ui", "code": "\"use client\";\n\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport Link from \"next/link\";\nimport { ChevronDown } from \"lucide-react\";\nimport { useState } from \"react\";\nimport { cn } from \"@/lib/utils\";\n\ninterface TimelineItem {\n  date: string;\n  title: string;\n  description?: string;\n  href?: string;\n  icon?: React.ReactNode;\n}\n\ninterface TimelineProps {\n  items: TimelineItem[];\n  initialCount?: number;\n  dateFormat?: Intl.DateTimeFormatOptions;\n  className?: string;\n  showMoreText?: string;\n  showLessText?: string;\n  dotClassName?: string;\n  lineClassName?: string;\n  titleClassName?: string;\n  descriptionClassName?: string;\n  dateClassName?: string;\n  buttonVariant?: \"default\" | \"outline\" | \"ghost\" | \"link\";\n  buttonSize?: \"default\" | \"sm\" | \"lg\";\n  animationDuration?: number;\n  animationDelay?: number;\n  showAnimation?: boolean;\n}\n\nfunction DesktopTimelineEntry({\n  item,\n  dotClassName,\n  lineClassName,\n  titleClassName,\n  descriptionClassName,\n  dateClassName,\n}: {\n  item: TimelineItem;\n  dotClassName?: string;\n  lineClassName?: string;\n  titleClassName?: string;\n  descriptionClassName?: string;\n  dateClassName?: string;\n}) {\n  return (\n    <Link\n      href={item.href || \"#\"}\n      className={cn(\n        \"group hidden grid-cols-9 items-center md:grid\",\n        !item.href && \"pointer-events-none\"\n      )}\n    >\n      <dl className=\"col-span-2\">\n        <dt className=\"sr-only\">Date</dt>\n        <dd\n          className={cn(\n            \"text-base font-medium text-muted-foreground transition-colors group-hover:text-foreground\",\n            dateClassName\n          )}\n        >\n          <time dateTime={item.date}>\n            {new Date(item.date).toLocaleDateString(\"en-US\", {\n              month: \"long\",\n              day: \"numeric\",\n              year: \"numeric\",\n            })}\n          </time>\n        </dd>\n      </dl>\n      <div className=\"col-span-7 flex items-center\">\n        <div className=\"relative ml-4\">\n          <div\n            className={cn(\"h-16 border-l border-border pr-8\", lineClassName)}\n          />\n          <div\n            className={cn(\n              \"absolute -left-1 top-[1.6875rem] flex h-5 w-5 items-center justify-center rounded-full bg-primary/60 transition-colors group-hover:bg-primary\",\n              !item.icon && \"h-2.5 w-2.5\",\n              dotClassName\n            )}\n          >\n            {item.icon && (\n              <div className=\"h-3 w-3 text-primary-foreground\">{item.icon}</div>\n            )}\n          </div>\n        </div>\n        <div className=\"flex flex-col gap-1\">\n          <h3\n            className={cn(\n              \"text-xl font-medium tracking-tight text-muted-foreground transition-colors group-hover:text-foreground\",\n              titleClassName\n            )}\n          >\n            {item.title}\n          </h3>\n          {item.description && (\n            <p\n              className={cn(\n                \"text-sm text-muted-foreground group-hover:text-muted-foreground/80\",\n                descriptionClassName\n              )}\n            >\n              {item.description}\n            </p>\n          )}\n        </div>\n      </div>\n    </Link>\n  );\n}\n\nfunction MobileTimelineEntry({\n  item,\n  dotClassName,\n  lineClassName,\n  titleClassName,\n  descriptionClassName,\n  dateClassName,\n}: {\n  item: TimelineItem;\n  dotClassName?: string;\n  lineClassName?: string;\n  titleClassName?: string;\n  descriptionClassName?: string;\n  dateClassName?: string;\n}) {\n  return (\n    <Link\n      href={item.href || \"#\"}\n      className={cn(\n        \"flex items-center space-x-4 rounded-lg px-4 py-3 transition-colors hover:bg-muted active:bg-muted/80 md:hidden\",\n        !item.href && \"pointer-events-none\"\n      )}\n    >\n      <div className=\"relative\">\n        <div className={cn(\"h-16 border-l border-border\", lineClassName)} />\n        <div\n          className={cn(\n            \"absolute -left-1 top-5 flex h-5 w-5 items-center justify-center rounded-full bg-primary/60\",\n            !item.icon && \"h-2.5 w-2.5\",\n            dotClassName\n          )}\n        >\n          {item.icon && (\n            <div className=\"h-3 w-3 text-primary-foreground\">{item.icon}</div>\n          )}\n        </div>\n      </div>\n      <div>\n        <dl>\n          <dt className=\"sr-only\">Date</dt>\n          <dd\n            className={cn(\n              \"text-sm font-medium text-muted-foreground\",\n              dateClassName\n            )}\n          >\n            <time dateTime={item.date}>\n              {new Date(item.date).toLocaleDateString(\"en-US\", {\n                month: \"long\",\n                day: \"numeric\",\n                year: \"numeric\",\n              })}\n            </time>\n          </dd>\n        </dl>\n        <h3\n          className={cn(\n            \"text-lg font-medium tracking-tight text-foreground\",\n            titleClassName\n          )}\n        >\n          {item.title}\n        </h3>\n        {item.description && (\n          <p\n            className={cn(\n              \"text-sm text-muted-foreground\",\n              descriptionClassName\n            )}\n          >\n            {item.description}\n          </p>\n        )}\n      </div>\n    </Link>\n  );\n}\n\nexport function Timeline({\n  items,\n  initialCount = 5,\n  className,\n  showMoreText = \"Show More\",\n  showLessText = \"Show Less\",\n  dotClassName,\n  lineClassName,\n  titleClassName,\n  descriptionClassName,\n  dateClassName,\n  buttonVariant = \"ghost\",\n  buttonSize = \"sm\",\n  animationDuration = 0.3,\n  animationDelay = 0.1,\n  showAnimation = true,\n}: TimelineProps) {\n  const [showAll, setShowAll] = useState(false);\n  const sortedItems = items.sort(\n    (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()\n  );\n  const initialItems = sortedItems.slice(0, initialCount);\n  const remainingItems = sortedItems.slice(initialCount);\n\n  return (\n    <div className={cn(\"mx-5 max-w-2xl md:mx-auto\", className)}>\n      <div className=\"md:translate-x-28\">\n        <ul className=\"space-y-8\">\n          {initialItems.map((item, index) => (\n            <motion.li\n              key={index}\n              initial={showAnimation ? { opacity: 0, y: 20 } : false}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{\n                duration: animationDuration,\n                delay: index * animationDelay,\n              }}\n            >\n              <DesktopTimelineEntry\n                item={item}\n                dotClassName={dotClassName}\n                lineClassName={lineClassName}\n                titleClassName={titleClassName}\n                descriptionClassName={descriptionClassName}\n                dateClassName={dateClassName}\n              />\n              <MobileTimelineEntry\n                item={item}\n                dotClassName={dotClassName}\n                lineClassName={lineClassName}\n                titleClassName={titleClassName}\n                descriptionClassName={descriptionClassName}\n                dateClassName={dateClassName}\n              />\n            </motion.li>\n          ))}\n          <AnimatePresence>\n            {showAll &&\n              remainingItems.map((item, index) => (\n                <motion.li\n                  key={index}\n                  initial={{ opacity: 0, height: 0 }}\n                  animate={{ opacity: 1, height: \"auto\" }}\n                  exit={{ opacity: 0, height: 0 }}\n                  transition={{\n                    duration: animationDuration,\n                    delay: index * animationDelay,\n                  }}\n                >\n                  <DesktopTimelineEntry\n                    item={item}\n                    dotClassName={dotClassName}\n                    lineClassName={lineClassName}\n                    titleClassName={titleClassName}\n                    descriptionClassName={descriptionClassName}\n                    dateClassName={dateClassName}\n                  />\n                  <MobileTimelineEntry\n                    item={item}\n                    dotClassName={dotClassName}\n                    lineClassName={lineClassName}\n                    titleClassName={titleClassName}\n                    descriptionClassName={descriptionClassName}\n                    dateClassName={dateClassName}\n                  />\n                </motion.li>\n              ))}\n          </AnimatePresence>\n        </ul>\n      </div>\n      {remainingItems.length > 0 && (\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          className=\"mt-8 flex justify-center\"\n        >\n          <Button\n            variant={buttonVariant}\n            size={buttonSize}\n            className=\"gap-2\"\n            onClick={() => setShowAll(!showAll)}\n          >\n            {showAll ? showLessText : showMoreText}\n            <motion.div\n              animate={{ rotate: showAll ? 180 : 0 }}\n              transition={{ duration: 0.2 }}\n            >\n              <ChevronDown className=\"h-4 w-4\" />\n            </motion.div>\n          </Button>\n        </motion.div>\n      )}\n    </div>\n  );\n}\n", "files": [{"path": "components/prismui/timeline.tsx", "type": "registry:ui"}], "dependencies": ["framer-motion", "lucide-react"], "category": "components", "subcategory": "display"}]