import { Icons } from "@/components/icons";
import { siteConfig } from "@/lib/config";

export default function Footer() {
  return (
    <footer className="border-t py-12 md:px-8">
      <div className="container">
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Icons.logo className="h-8 w-8" />
              <span className="font-bold text-lg">{siteConfig.name}</span>
            </div>
            <p className="text-sm text-muted-foreground">
              Safeguarding life, property and environment through quality assurance and total quality management.
            </p>
            <div className="flex items-center gap-4">
              <a
                href={`https://facebook.com/nacecertification`}
                target="_blank"
                rel="noreferrer"
                className="text-muted-foreground hover:text-foreground"
              >
                <Icons.facebook className="h-5 w-5" />
                <span className="sr-only">Facebook</span>
              </a>
              <a
                href={`https://instagram.com/nacecertification`}
                target="_blank"
                rel="noreferrer"
                className="text-muted-foreground hover:text-foreground"
              >
                <Icons.instagram className="h-5 w-5" />
                <span className="sr-only">Instagram</span>
              </a>
            </div>
          </div>

          {/* Services */}
          <div className="space-y-4">
            <h3 className="font-medium">Services</h3>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li><a href="/iso-9001" className="hover:text-foreground">ISO 9001:2015</a></li>
              <li><a href="/iso-27001" className="hover:text-foreground">ISO/IEC 27001:2022</a></li>
              <li><a href="/iso-20000" className="hover:text-foreground">ISO/IEC 20000-1:2018</a></li>
              <li><a href="/iso-21001" className="hover:text-foreground">ISO 21001:2018</a></li>
            </ul>
          </div>

          {/* Company */}
          <div className="space-y-4">
            <h3 className="font-medium">Company</h3>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li><a href="/about" className="hover:text-foreground">About Us</a></li>
              <li><a href="/certification-process" className="hover:text-foreground">Certification Process</a></li>
              <li><a href="/accreditation" className="hover:text-foreground">Accreditation</a></li>
              <li><a href="/contact" className="hover:text-foreground">Contact</a></li>
            </ul>
          </div>

          {/* Contact */}
          <div className="space-y-4">
            <h3 className="font-medium">Contact</h3>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li className="flex items-center gap-2">
                <Icons.phone className="h-4 w-4" />
                <a href={`tel:${siteConfig.links.phone}`} className="hover:text-foreground">
                  {siteConfig.links.phone}
                </a>
              </li>
              <li className="flex items-center gap-2">
                <Icons.mail className="h-4 w-4" />
                <a href={`mailto:${siteConfig.links.email}`} className="hover:text-foreground">
                  {siteConfig.links.email}
                </a>
              </li>
              <li className="flex items-center gap-2">
                <Icons.mapPin className="h-4 w-4" />
                <span>{siteConfig.links.address}</span>
              </li>
              <li className="flex items-center gap-2">
                <Icons.messageCircle className="h-4 w-4" />
                <a href={`https://api.whatsapp.com/send?phone=${siteConfig.links.whatsapp}`} className="hover:text-foreground">
                  WhatsApp
                </a>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-8 pt-8 border-t">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="text-sm text-muted-foreground">
              © 2024 NACE Certification Pvt. Ltd. All rights reserved.
            </p>
            <p className="text-sm text-muted-foreground">
              Accredited by United Accreditation Foundation
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}
