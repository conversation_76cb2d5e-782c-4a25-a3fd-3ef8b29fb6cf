"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Icons } from "@/components/icons";
import Link from "next/link";

const values = [
  {
    icon: <Icons.shield className="h-6 w-6" />,
    title: "Safety & Quality",
    description: "Dedicated to safeguarding life, property, and environment through stringent quality assurance."
  },
  {
    icon: <Icons.users className="h-6 w-6" />,
    title: "Team Excellence",
    description: "Every employee contributes to all aspects of our business process for continuous improvement."
  },
  {
    icon: <Icons.target className="h-6 w-6" />,
    title: "Customer Satisfaction",
    description: "Strong feedback system from clients, auditors and certification staff ensures quality service."
  },
  {
    icon: <Icons.balance className="h-6 w-6" />,
    title: "Impartial Certification",
    description: "We offer certification without bias, discrimination or prejudice for fair and credible results."
  }
];

const stats = [
  {
    number: "500+",
    label: "Certified Organizations",
    description: "Successfully certified organizations across various industries"
  },
  {
    number: "15+",
    label: "Years Experience",
    description: "Years of expertise in quality management and certification"
  },
  {
    number: "4",
    label: "ISO Standards",
    description: "Comprehensive certification services for major ISO standards"
  },
  {
    number: "100%",
    label: "Accredited",
    description: "Fully accredited by United Accreditation Foundation"
  }
];

export function About() {
  return (
    <section className="container relative py-20">
      <div className="grid gap-16 lg:grid-cols-2 lg:gap-24 items-center">
        {/* Content */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, ease: [0.16, 1, 0.3, 1] }}
          viewport={{ once: true }}
          className="space-y-8"
        >
          <div className="space-y-4">
            <Button variant="outline" className="rounded-full">
              About NACE Certification
            </Button>
            <h2 className="text-3xl font-bold tracking-tight sm:text-5xl">
              Leading Certification Body for Quality Excellence
            </h2>
            <p className="text-xl text-muted-foreground">
              NACE Certification Pvt. Ltd. is established as a certification body with the main objective to safeguard life, property and environment through quality assurance and total quality management.
            </p>
          </div>

          <div className="space-y-6">
            <p className="text-muted-foreground">
              We create an environment where each employee contributes to all aspects of our business process and strive for continuous improvement to meet customer satisfaction by having a strong feedback system from clients, auditors and certification staff.
            </p>
            
            <div className="grid gap-4 sm:grid-cols-2">
              {values.map((value, index) => (
                <motion.div
                  key={value.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{
                    duration: 0.6,
                    delay: index * 0.1,
                    ease: [0.16, 1, 0.3, 1],
                  }}
                  viewport={{ once: true }}
                  className="flex gap-3"
                >
                  <div className="flex-shrink-0 w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center text-primary">
                    {value.icon}
                  </div>
                  <div className="space-y-1">
                    <h3 className="font-medium">{value.title}</h3>
                    <p className="text-sm text-muted-foreground">{value.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4">
            <Link href="/contact">
              <Button size="lg" className="gap-2">
                <Icons.phone className="h-4 w-4" />
                Contact Us
              </Button>
            </Link>
            <Link href="/services">
              <Button variant="outline" size="lg" className="gap-2">
                <Icons.component className="h-4 w-4" />
                Our Services
              </Button>
            </Link>
          </div>
        </motion.div>

        {/* Stats */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, ease: [0.16, 1, 0.3, 1] }}
          viewport={{ once: true }}
          className="space-y-8"
        >
          <Card className="border-none bg-background/50 backdrop-blur p-8">
            <CardContent className="p-0 space-y-8">
              <div className="space-y-4">
                <h3 className="text-2xl font-bold">Our Impact</h3>
                <p className="text-muted-foreground">
                  Trusted by organizations worldwide for quality certification and management system excellence.
                </p>
              </div>
              
              <div className="grid gap-6 sm:grid-cols-2">
                {stats.map((stat, index) => (
                  <motion.div
                    key={stat.label}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{
                      duration: 0.6,
                      delay: index * 0.1,
                      ease: [0.16, 1, 0.3, 1],
                    }}
                    viewport={{ once: true }}
                    className="space-y-2"
                  >
                    <div className="text-3xl font-bold text-primary">{stat.number}</div>
                    <div className="font-medium">{stat.label}</div>
                    <div className="text-sm text-muted-foreground">{stat.description}</div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="border-none bg-primary/5 backdrop-blur p-6">
            <CardContent className="p-0 space-y-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                  <Icons.award className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h4 className="font-medium">Accredited by</h4>
                  <p className="text-sm text-muted-foreground">United Accreditation Foundation</p>
                </div>
              </div>
              <p className="text-sm text-muted-foreground">
                Our accreditation ensures that our certification processes meet the highest international standards and are recognized globally.
              </p>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  );
}
