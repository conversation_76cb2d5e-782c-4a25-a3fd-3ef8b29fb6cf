"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Icons } from "@/components/icons";

const testimonials = [
  {
    quote: "The NACE training was incredibly detailed and practical. The instructors had real-world experience, and I felt fully prepared for the certification exam. Highly recommend to anyone in the coatings or corrosion field!",
    author: "<PERSON><PERSON><PERSON>",
    role: "Quality Engineer",
    company: "Hydrocarbon Industries",
    avatar: "/avatars/rakesh.jpg",
    rating: 5
  },
  {
    quote: "NACE Certification provided excellent support throughout our ISO 9001:2015 certification process. Their team was professional, knowledgeable, and helped us achieve certification efficiently.",
    author: "<PERSON><PERSON>",
    role: "Operations Manager",
    company: "Tech Solutions Ltd.",
    avatar: "/avatars/priya.jpg",
    rating: 5
  },
  {
    quote: "The audit process was thorough yet smooth. NACE's auditors were experienced and provided valuable insights that helped improve our quality management system beyond just certification.",
    author: "<PERSON><PERSON>",
    role: "Quality Director",
    company: "Manufacturing Corp",
    avatar: "/avatars/amit.jpg",
    rating: 5
  },
  {
    quote: "Outstanding service for ISO 27001:2022 certification. The team guided us through every step and ensured we understood the requirements. Our information security has significantly improved.",
    author: "Sarah Johnson",
    role: "IT Security Manager",
    company: "Digital Services Inc.",
    avatar: "/avatars/sarah.jpg",
    rating: 5
  },
  {
    quote: "NACE's impartial and professional approach to certification gave us confidence in the process. The continuous support and feedback system is exceptional.",
    author: "Rajesh Patel",
    role: "General Manager",
    company: "Industrial Solutions",
    avatar: "/avatars/rajesh.jpg",
    rating: 5
  },
  {
    quote: "The ISO 21001:2018 certification process was handled expertly. NACE understood our educational organization's unique needs and provided tailored guidance throughout.",
    author: "Dr. Meera Singh",
    role: "Academic Director",
    company: "Education Institute",
    avatar: "/avatars/meera.jpg",
    rating: 5
  }
];

interface TestimonialCardProps {
  quote: string;
  author: string;
  role: string;
  company: string;
  avatar: string;
  rating: number;
  index: number;
}

function TestimonialCard({
  quote,
  author,
  role,
  company,
  avatar,
  rating,
  index,
}: TestimonialCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{
        duration: 0.8,
        delay: index * 0.1,
        ease: [0.16, 1, 0.3, 1],
      }}
      viewport={{ once: true }}
    >
      <Card className="h-full border-none bg-background/50 backdrop-blur">
        <CardContent className="p-6 space-y-4">
          <div className="flex gap-1">
            {Array.from({ length: rating }).map((_, i) => (
              <Icons.star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
            ))}
          </div>
          <blockquote className="text-muted-foreground italic">
            "{quote}"
          </blockquote>
          <div className="flex items-center gap-3">
            <Avatar className="h-10 w-10">
              <AvatarImage src={avatar} alt={author} />
              <AvatarFallback>{author.split(' ').map(n => n[0]).join('')}</AvatarFallback>
            </Avatar>
            <div>
              <div className="font-medium">{author}</div>
              <div className="text-sm text-muted-foreground">{role}, {company}</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

export function Testimonials() {
  return (
    <section className="container relative py-20">
      <div className="text-center mb-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: [0.16, 1, 0.3, 1] }}
          viewport={{ once: true }}
        >
          <Button variant="outline" className="rounded-full mb-4">
            Client Testimonials
          </Button>
          <h2 className="text-3xl font-bold tracking-tight sm:text-5xl mb-4">
            What Our Clients Say
          </h2>
          <p className="text-xl text-muted-foreground max-w-[800px] mx-auto">
            Hear from organizations that have achieved certification excellence with NACE. Their success stories reflect our commitment to quality and customer satisfaction.
          </p>
        </motion.div>
      </div>
      
      <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
        {testimonials.map((testimonial, index) => (
          <TestimonialCard key={testimonial.author} {...testimonial} index={index} />
        ))}
      </div>

      <div className="text-center mt-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4, ease: [0.16, 1, 0.3, 1] }}
          viewport={{ once: true }}
          className="space-y-4"
        >
          <p className="text-muted-foreground">
            Ready to join our satisfied clients?
          </p>
          <Button size="lg" className="gap-2">
            <Icons.phone className="h-4 w-4" />
            Start Your Certification
          </Button>
        </motion.div>
      </div>
    </section>
  );
}
