"use client";

import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Icons } from "@/components/icons";
import Link from "next/link";

const services = [
  {
    title: "ISO 9001:2015",
    subtitle: "Quality Management System",
    description: "Demonstrates your organization's ability to consistently provide products and services that meet customer and regulatory requirements.",
    features: [
      "Quality Management Concept & Definition",
      "Quality Management Systems",
      "The Audit Process",
      "Contexts of Organization and Leadership",
      "Planning & Support"
    ],
    icon: <Icons.shield className="h-8 w-8" />,
    color: "bg-blue-500/10 text-blue-600",
    href: "/iso-9001"
  },
  {
    title: "ISO/IEC 27001:2022",
    subtitle: "Information Security Management",
    description: "Helps organizations protect their information assets and manage security risks through a systematic approach to information security.",
    features: [
      "Information Security Management",
      "Risk Assessment & Treatment",
      "Security Controls Implementation",
      "Incident Management",
      "Continuous Monitoring"
    ],
    icon: <Icons.lock className="h-8 w-8" />,
    color: "bg-green-500/10 text-green-600",
    href: "/iso-27001"
  },
  {
    title: "ISO/IEC 20000-1:2018",
    subtitle: "IT Service Management",
    description: "Demonstrates your organization's ability to deliver managed IT services of an acceptable quality to your customers and users.",
    features: [
      "Service Management System",
      "Service Delivery Processes",
      "Relationship Processes",
      "Resolution Processes",
      "Control Processes"
    ],
    icon: <Icons.server className="h-8 w-8" />,
    color: "bg-purple-500/10 text-purple-600",
    href: "/iso-20000"
  },
  {
    title: "ISO 21001:2018",
    subtitle: "Educational Organizations Management",
    description: "Provides a management tool for organizations that provide educational products and services capable of supporting all learners.",
    features: [
      "Educational Management System",
      "Learner-Centered Approach",
      "Stakeholder Engagement",
      "Continuous Improvement",
      "Educational Effectiveness"
    ],
    icon: <Icons.graduationCap className="h-8 w-8" />,
    color: "bg-orange-500/10 text-orange-600",
    href: "/iso-21001"
  }
];

interface ServiceCardProps {
  title: string;
  subtitle: string;
  description: string;
  features: string[];
  icon: React.ReactNode;
  color: string;
  href: string;
  index: number;
}

function ServiceCard({
  title,
  subtitle,
  description,
  features,
  icon,
  color,
  href,
  index,
}: ServiceCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{
        duration: 0.8,
        delay: index * 0.1,
        ease: [0.16, 1, 0.3, 1],
      }}
      viewport={{ once: true }}
    >
      <Card className="h-full border-none bg-background/50 backdrop-blur hover:shadow-lg transition-all duration-300">
        <CardHeader>
          <div className={`inline-flex h-12 w-12 items-center justify-center rounded-lg ${color} mb-4`}>
            {icon}
          </div>
          <div className="space-y-2">
            <Badge variant="secondary" className="text-xs">
              {title}
            </Badge>
            <CardTitle className="text-xl font-bold">{subtitle}</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-muted-foreground">{description}</p>
          <div className="space-y-2">
            <h4 className="font-medium text-sm">Key Areas:</h4>
            <ul className="space-y-1">
              {features.slice(0, 3).map((feature, idx) => (
                <li key={idx} className="text-sm text-muted-foreground flex items-center gap-2">
                  <Icons.check className="h-3 w-3 text-green-500" />
                  {feature}
                </li>
              ))}
              {features.length > 3 && (
                <li className="text-sm text-muted-foreground">
                  +{features.length - 3} more areas
                </li>
              )}
            </ul>
          </div>
          <Link href={href}>
            <Button variant="outline" className="w-full mt-4">
              Learn More
              <Icons.arrowRight className="h-4 w-4 ml-2" />
            </Button>
          </Link>
        </CardContent>
      </Card>
    </motion.div>
  );
}

export function Services() {
  return (
    <section className="container relative py-20">
      <div className="text-center mb-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: [0.16, 1, 0.3, 1] }}
          viewport={{ once: true }}
        >
          <Button variant="outline" className="rounded-full mb-4">
            ISO Certifications
          </Button>
          <h2 className="text-3xl font-bold tracking-tight sm:text-5xl mb-4">
            Our Certification Services
          </h2>
          <p className="text-xl text-muted-foreground max-w-[800px] mx-auto">
            NACE Certification offers comprehensive ISO certification services to help your organization achieve excellence in quality management, information security, and operational efficiency.
          </p>
        </motion.div>
      </div>
      <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-2">
        {services.map((service, index) => (
          <ServiceCard key={service.title} {...service} index={index} />
        ))}
      </div>
      <div className="text-center mt-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4, ease: [0.16, 1, 0.3, 1] }}
          viewport={{ once: true }}
        >
          <Link href="/contact">
            <Button size="lg" className="gap-2">
              <Icons.phone className="h-4 w-4" />
              Start Your Certification Journey
            </Button>
          </Link>
        </motion.div>
      </div>
    </section>
  );
}
