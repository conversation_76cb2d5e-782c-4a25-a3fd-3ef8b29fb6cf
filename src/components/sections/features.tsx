"use client";

import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

const features = [
  {
    number: "01",
    title: "Commitment to Safety & Quality",
    description:
      "We are dedicated to safeguarding life, property, and the environment through stringent quality assurance and total quality management practices.",
    icon: (
      <div className="relative w-full h-32 mx-auto grid grid-cols-2 gap-3">
        <div className="space-y-3">
          <div className="h-8 rounded-md bg-green-500/20 animate-pulse" />
          <div className="h-8 rounded-md bg-green-500/20 animate-pulse delay-100" />
        </div>
        <div className="space-y-3">
          <div className="h-8 rounded-md bg-green-500/20 animate-pulse delay-200" />
          <div className="h-8 rounded-md bg-green-500/20 animate-pulse delay-300" />
        </div>
      </div>
    ),
  },
  {
    number: "02",
    title: "Impartial & Transparent Certification",
    description:
      "Our certification processes are conducted without bias, discrimination, or prejudice—ensuring fair and credible results every time for all our clients.",
    icon: (
      <div className="relative w-full h-32 mx-auto">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-full max-w-[240px] rounded-lg bg-muted/50 p-4">
            <div className="space-y-2">
              <div className="h-2 w-1/2 rounded bg-blue-500/20" />
              <div className="h-2 w-full rounded bg-blue-500/20" />
              <div className="h-2 w-3/4 rounded bg-blue-500/20" />
            </div>
            <div className="mt-4 grid grid-cols-2 gap-2">
              <div className="h-6 rounded bg-blue-500/20" />
              <div className="h-6 rounded bg-blue-500/20" />
            </div>
          </div>
        </div>
      </div>
    ),
  },
  {
    number: "03",
    title: "Trusted Certification Body",
    description:
      "As an established and recognized certification authority, we offer reliable recognition of management systems across industries with continuous improvement culture.",
    icon: (
      <div className="relative w-full h-32 mx-auto">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="flex items-center gap-3">
            <div className="h-16 w-16 rounded-full bg-yellow-500/20 animate-bounce delay-100" />
            <div className="h-16 w-16 rounded-full bg-yellow-500/20 animate-bounce delay-200" />
            <div className="h-16 w-16 rounded-full bg-yellow-500/20 animate-bounce delay-300" />
          </div>
        </div>
      </div>
    ),
  },
];

function FeatureCard({
  number,
  title,
  description,
  icon,
  index,
}: {
  number: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  index: number;
}) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{
        duration: 0.8,
        delay: index * 0.2,
        ease: [0.16, 1, 0.3, 1],
      }}
      viewport={{ once: true }}
    >
      <Card className="border-none bg-background/50 backdrop-blur">
        <CardHeader>
          <div className="mb-4 inline-flex h-8 w-8 items-center justify-center rounded-lg bg-muted text-sm font-medium">
            {number}
          </div>
          <CardTitle className="text-xl font-bold">{title}</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground mb-8">{description}</p>
          {icon}
        </CardContent>
      </Card>
    </motion.div>
  );
}

export function Features() {
  return (
    <section className="container relative py-20">
      <div className="text-center mb-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: [0.16, 1, 0.3, 1] }}
          viewport={{ once: true }}
        >
          <Button variant="outline" className="rounded-full mb-4">
            Why Choose Us
          </Button>
          <h2 className="text-3xl font-bold tracking-tight sm:text-5xl mb-4">
            Excellence in Quality Assurance & Certification
          </h2>
          <p className="text-xl text-muted-foreground max-w-[800px] mx-auto">
            NACE Certification creates an environment where each employee contributes to all aspects of our business process and strives for continuous improvement to meet customer satisfaction.
          </p>
        </motion.div>
      </div>
      <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
        {features.map((feature, index) => (
          <FeatureCard key={feature.number} {...feature} index={index} />
        ))}
      </div>
    </section>
  );
}
