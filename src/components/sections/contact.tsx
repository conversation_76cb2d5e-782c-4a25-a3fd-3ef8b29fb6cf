"use client";

import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Icons } from "@/components/icons";
import Link from "next/link";

const contactInfo = [
  {
    icon: <Icons.phone className="h-5 w-5" />,
    title: "Phone",
    details: "+91-120-3170247",
    description: "Call us for immediate assistance",
    href: "tel:+************"
  },
  {
    icon: <Icons.mail className="h-5 w-5" />,
    title: "Email",
    details: "<EMAIL>",
    description: "Send us your queries",
    href: "mailto:<EMAIL>"
  },
  {
    icon: <Icons.mapPin className="h-5 w-5" />,
    title: "Address",
    details: "6C, Block-2, Sector-135",
    description: "Noida-201304, India",
    href: "https://maps.google.com/?q=6C+Block-2+Sector-135+Noida-201304"
  },
  {
    icon: <Icons.messageCircle className="h-5 w-5" />,
    title: "WhatsApp",
    details: "+91-8285008681",
    description: "Chat with us instantly",
    href: "https://api.whatsapp.com/send?phone=+************"
  }
];

const services = [
  "ISO 9001:2015 Certification",
  "ISO/IEC 27001:2022 Certification", 
  "ISO/IEC 20000-1:2018 Certification",
  "ISO 21001:2018 Certification",
  "Audit & Assessment Services",
  "Training & Consultation"
];

export function Contact() {
  return (
    <section className="container relative py-20">
      <div className="text-center mb-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: [0.16, 1, 0.3, 1] }}
          viewport={{ once: true }}
        >
          <Button variant="outline" className="rounded-full mb-4">
            Get In Touch
          </Button>
          <h2 className="text-3xl font-bold tracking-tight sm:text-5xl mb-4">
            Contact NACE Certification
          </h2>
          <p className="text-xl text-muted-foreground max-w-[800px] mx-auto">
            Ready to start your certification journey? Our team of experts is here to guide you through the process and answer all your questions.
          </p>
        </motion.div>
      </div>

      <div className="grid gap-12 lg:grid-cols-2">
        {/* Contact Information */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, ease: [0.16, 1, 0.3, 1] }}
          viewport={{ once: true }}
          className="space-y-8"
        >
          <div className="space-y-6">
            <h3 className="text-2xl font-bold">Get in Touch</h3>
            <p className="text-muted-foreground">
              We're here to help you achieve certification excellence. Reach out to us through any of the following channels:
            </p>
          </div>

          <div className="grid gap-4 sm:grid-cols-2">
            {contactInfo.map((info, index) => (
              <motion.div
                key={info.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{
                  duration: 0.6,
                  delay: index * 0.1,
                  ease: [0.16, 1, 0.3, 1],
                }}
                viewport={{ once: true }}
              >
                <Link href={info.href} className="block">
                  <Card className="border-none bg-background/50 backdrop-blur hover:shadow-lg transition-all duration-300 h-full">
                    <CardContent className="p-6 space-y-3">
                      <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center text-primary">
                        {info.icon}
                      </div>
                      <div>
                        <h4 className="font-medium">{info.title}</h4>
                        <p className="text-sm font-medium text-primary">{info.details}</p>
                        <p className="text-xs text-muted-foreground">{info.description}</p>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              </motion.div>
            ))}
          </div>

          <Card className="border-none bg-primary/5 backdrop-blur">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icons.clock className="h-5 w-5" />
                Business Hours
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Monday - Friday</span>
                <span className="font-medium">9:00 AM - 6:00 PM</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Saturday</span>
                <span className="font-medium">9:00 AM - 2:00 PM</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Sunday</span>
                <span className="font-medium">Closed</span>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Contact Form */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, ease: [0.16, 1, 0.3, 1] }}
          viewport={{ once: true }}
        >
          <Card className="border-none bg-background/50 backdrop-blur">
            <CardHeader>
              <CardTitle>Send us a Message</CardTitle>
              <p className="text-muted-foreground">
                Fill out the form below and we'll get back to you within 24 hours.
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4 sm:grid-cols-2">
                <div className="space-y-2">
                  <label className="text-sm font-medium">First Name</label>
                  <Input placeholder="Enter your first name" />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Last Name</label>
                  <Input placeholder="Enter your last name" />
                </div>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Email</label>
                <Input type="email" placeholder="Enter your email address" />
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Phone</label>
                <Input type="tel" placeholder="Enter your phone number" />
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Service Interest</label>
                <select className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm">
                  <option value="">Select a service</option>
                  {services.map((service) => (
                    <option key={service} value={service}>{service}</option>
                  ))}
                </select>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Message</label>
                <Textarea 
                  placeholder="Tell us about your certification requirements..."
                  className="min-h-[120px]"
                />
              </div>
              
              <Button className="w-full gap-2">
                <Icons.send className="h-4 w-4" />
                Send Message
              </Button>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  );
}
