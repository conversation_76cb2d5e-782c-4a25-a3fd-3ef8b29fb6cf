---
title: Button Group
publishedAt: 2025-01-02
summary: A versatile button group component for organizing related actions with consistent spacing and visual connection.
author: motormary
slug: components/button-group
related: ["introduction"]
---

<ComponentPreview name="button-group-basic" />

<Note variant="info">
  The Button Group component provides an elegant way to group related buttons together with consistent spacing and visual connection. Perfect for navigation, toolbars, and multi-step forms.
</Note>

## Installation

<Tabs defaultValue="cli">
  <TabsList>
    <TabsTrigger value="cli">CLI</TabsTrigger>
    <TabsTrigger value="manual">Manual</TabsTrigger>
  </TabsList>
  <TabsContent value="cli">
    <ComponentSource name="button-group" isCli />
  </TabsContent>
  <TabsContent value="manual">
    <Steps>
      <Step>Copy and paste the following code into your project.</Step>
      <ComponentSource name="button-group" />
      <Step>Update the import paths to match your project setup.</Step>
    </Steps>
  </TabsContent>
</Tabs>

## Examples

<ComponentPreview name="button-group-demo" />

## Usage

### Basic Usage

```tsx
import { Button } from "@/components/ui/button"
import { ButtonGroup } from "@/components/prismui/button-group"

export default function Example() {
  return (
    <ButtonGroup>
      <Button>Previous</Button>
      <Button>Next</Button>
    </ButtonGroup>
  )
}
```

### With Icons

```tsx
import { Button } from "@/components/ui/button"
import { ButtonGroup } from "@/components/prismui/button-group"
import { Grid, List } from "lucide-react"

export default function Example() {
  return (
    <ButtonGroup size="icon">
      <Button variant="outline">
        <Grid className="h-4 w-4" />
      </Button>
      <Button variant="outline">
        <List className="h-4 w-4" />
      </Button>
    </ButtonGroup>
  )
}
```

## Customization

<Steps>
  <Step>Different Sizes</Step>
  ```tsx
  <ButtonGroup size="sm">
    <Button>Small</Button>
    <Button>Buttons</Button>
  </ButtonGroup>
  ```

  <Step>Separated Buttons</Step>
  ```tsx
  <ButtonGroup separated>
    <Button variant="outline">With</Button>
    <Button variant="outline">Gaps</Button>
  </ButtonGroup>
  ```

  <Step>Custom Styling</Step>
  ```tsx
  <ButtonGroup className="shadow-lg">
    <Button className="font-bold">Custom</Button>
    <Button className="font-bold">Styles</Button>
  </ButtonGroup>
  ```
</Steps>

## Notes

- Built with class-variance-authority for type-safe variants
- Responsive design with mobile-first approach
- Supports all button variants from Shadcn UI
- Maintains proper focus states and accessibility
- Handles touch devices with appropriate spacing
- Supports vertical stacking on mobile
- TypeScript support with proper types
- Customizable through Tailwind classes

## Features

- Multiple size variants
- Optional button separation
- Icon-only mode support
- Responsive layout
- Mobile-friendly design
- Focus management
- Keyboard navigation
- Consistent spacing

## Props

| Prop | Type | Description |
| --- | --- | --- |
| size | "default" \| "sm" \| "lg" \| "icon" | The size of the button group |
| separated | boolean | Whether to add spacing between buttons |
| className | string | Additional CSS classes |
| children | React.ReactNode | The buttons to be grouped |