---
title: Timeline
publishedAt: 2025-01-09
summary: A responsive timeline component for displaying chronological events with expandable content.
author: codehagen
slug: components/timeline
related: ["introduction"]
---

<ComponentPreview name="timeline-basic" />

<Note variant="info">
  The Timeline component provides an elegant way to display chronological events with a responsive layout, smooth animations, and expandable content. Perfect for changelogs, activity feeds, and project histories.
</Note>

## Installation

<Tabs defaultValue="cli">
  <TabsList>
    <TabsTrigger value="cli">CLI</TabsTrigger>
    <TabsTrigger value="manual">Manual</TabsTrigger>
  </TabsList>
  <TabsContent value="cli">
    <ComponentSource name="timeline" isCli />
  </TabsContent>
  <TabsContent value="manual">
    <Steps>
      <Step>Copy and paste the following code into your project.</Step>
      <ComponentSource name="timeline" />
      <Step>Update the import paths to match your project setup.</Step>
    </Steps>
  </TabsContent>
</Tabs>

## Usage

```tsx
import { Timeline } from "@/components/prismui/timeline"

export default function Example() {
  return (
    <Timeline
      items={[
        {
          date: "2024-01-07",
          title: "New Feature",
          description: "Added dark mode support",
          href: "/changelog/dark-mode",
          icon: <Sparkles className="h-3 w-3" />,
        },
        {
          date: "2024-01-05",
          title: "Bug Fix",
          description: "Fixed mobile navigation",
          href: "/changelog/mobile-nav",
          icon: <Code className="h-3 w-3" />,
        }
      ]}
      initialCount={3}
      showMoreText="Load More"
      showLessText="Show Less"
      dotClassName="bg-gradient-to-b from-background to-muted ring-1 ring-border"
      lineClassName="border-l border-border"
    />
  )
}
```

## Style Variations

### Modern Style

The modern style features subtle gradients, hover effects, and icons.

<ComponentPreview name="timeline-modern" />

```tsx
import { Timeline } from "@/components/prismui/timeline"
import { Sparkles, Code } from "lucide-react"

export default function TimelineModern() {
  const items = [
    {
      date: "2024-01-07",
      title: "Modern Style",
      description: "With subtle gradients and hover effects",
      href: "#modern",
      icon: <Sparkles className="h-3 w-3" />,
    },
    {
      date: "2024-01-05",
      title: "Minimal Style",
      description: "Clean and simple design",
      href: "#minimal",
      icon: <Code className="h-3 w-3" />,
    }
  ]

  return (
    <Timeline
      items={items}
      initialCount={2}
      dotClassName="bg-gradient-to-b from-background to-muted ring-1 ring-border group-hover:ring-primary group-hover:bg-gradient-to-b group-hover:from-muted group-hover:to-muted/50 transition-all duration-300"
      lineClassName="border-l border-border"
      titleClassName="font-medium text-foreground/90 group-hover:text-primary transition-colors duration-300"
      dateClassName="text-muted-foreground group-hover:text-foreground/80 transition-colors duration-300"
      descriptionClassName="text-muted-foreground/80"
      buttonVariant="outline"
      buttonSize="sm"
      showMoreText="Load More"
      showLessText="Show Less"
      animationDuration={0.4}
      animationDelay={0.15}
    />
  )
}
```

### Minimal Style

The minimal style offers a clean and simple design without icons.

<ComponentPreview name="timeline-minimal" />

```tsx
import { Timeline } from "@/components/prismui/timeline"

export default function TimelineMinimal() {
  const items = [
    {
      date: "2024-01-07",
      title: "Modern Style",
      description: "With subtle gradients and hover effects",
      href: "#modern",
    },
    {
      date: "2024-01-05",
      title: "Minimal Style",
      description: "Clean and simple design",
      href: "#minimal",
    }
  ]

  return (
    <Timeline
      items={items}
      initialCount={2}
      dotClassName="bg-muted-foreground/20 group-hover:bg-muted-foreground/40 h-2 w-2 transition-colors duration-300"
      lineClassName="border-l border-border/50"
      titleClassName="font-normal"
      dateClassName="font-light"
      buttonVariant="ghost"
      buttonSize="sm"
      showMoreText="View More"
      showLessText="View Less"
      animationDuration={0.3}
      animationDelay={0.1}
    />
  )
}
```

## Customization

<Steps>
  <Step>Custom Styling</Step>
  ```tsx
  <Timeline
    className="my-custom-timeline"
    dotClassName="bg-primary/60 group-hover:bg-primary"
    lineClassName="border-l-2 border-primary/20"
    titleClassName="font-medium text-primary"
    dateClassName="text-muted-foreground"
    items={items}
  />
  ```

  <Step>Custom Button Text</Step>
  ```tsx
  <Timeline
    showMoreText="Load More Events"
    showLessText="Collapse Events"
    buttonVariant="outline"
    buttonSize="sm"
    items={items}
  />
  ```

  <Step>Animation Settings</Step>
  ```tsx
  <Timeline
    animationDuration={0.4}
    animationDelay={0.15}
    showAnimation={true}
    items={items}
  />
  ```
</Steps>


## Props

| Prop | Type | Default | Description |
| --- | --- | --- | --- |
| `items` | `TimelineItem[]` | Required | Array of timeline items to display |
| `initialCount` | `number` | `5` | Number of items to show initially |
| `className` | `string` | - | Additional CSS classes |
| `showMoreText` | `string` | "Show More" | Text for the show more button |
| `showLessText` | `string` | "Show Less" | Text for the show less button |
| `dotClassName` | `string` | - | Classes for the timeline dots |
| `lineClassName` | `string` | - | Classes for the timeline lines |
| `titleClassName` | `string` | - | Classes for item titles |
| `descriptionClassName` | `string` | - | Classes for item descriptions |
| `dateClassName` | `string` | - | Classes for dates |
| `buttonVariant` | `"default" \| "outline" \| "ghost" \| "link"` | `"ghost"` | Button variant |
| `buttonSize` | `"default" \| "sm" \| "lg"` | `"sm"` | Button size |
| `animationDuration` | `number` | `0.3` | Animation duration in seconds |
| `animationDelay` | `number` | `0.1` | Delay between animations |
| `showAnimation` | `boolean` | `true` | Whether to show animations |

## Features

- Responsive design with mobile-first approach
- Smooth animations using Framer Motion
- Expandable content with "Show More/Less"
- Automatic date sorting (newest first)
- Customizable text and styling
- Support for icons in timeline dots
- Accessible markup with proper ARIA labels
- Optional links for each timeline item
- Hover states and transitions