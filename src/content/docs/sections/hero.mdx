---
title: Hero Section
publishedAt: 2025-01-02
summary: A collection of hero section components for creating impactful landing pages with animations and modern design.
author: codehagen
slug: sections/hero
---

<SectionPreview name="hero-test" />

<Note>
  Hero sections are crucial for making a strong first impression. This component combines modern design with smooth animations to create an engaging user experience.
</Note>

## Features

- Animated gradient background with Framer Motion
- Responsive text reveal animations
- Call-to-action buttons with hover effects
- 3D card animation with reflection effect
- Dark mode compatible

## Installation

<Steps>
  <Step>Copy the Hero component code</Step>
  ```tsx:components/sections/hero.tsx
  "use client";

  import { motion } from "framer-motion";
  import { cn } from "@/lib/utils";
  import { Button } from "@/components/ui/button";
  import WordReveal from "@/components/prismui/word-reveal";

  interface HeroProps {
    className?: string;
    children?: React.ReactNode;
  }

  export default function Hero({ className, children }: HeroProps) {
    return (
      <div
        className={cn(
          "relative min-h-screen flex flex-col items-center justify-center overflow-hidden bg-black",
          className
        )}
      >
        {/* Gradient Background */}
        <div className="absolute inset-0 w-full h-full bg-gradient-to-b from-black via-zinc-900/50 to-black" />

        {/* Animated Gradient Blob */}
        <motion.div
          className="absolute w-[1000px] h-[1000px] rounded-full bg-gradient-to-r from-violet-500/30 to-fuchsia-500/30 blur-3xl"
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.3, 0.2, 0.3],
            x: [0, 100, 0],
            y: [0, 50, 0],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "linear",
          }}
        />

        {/* Content Container */}
        <div className="relative z-10 max-w-5xl mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1 }}
          >
            <WordReveal
              text="Linear is a better way to build products"
              className="mb-6"
              delay={0.2}
            />

            <motion.p
              initial={{ opacity: 0, y: 20, filter: "blur(10px)" }}
              animate={{ opacity: 1, y: 0, filter: "blur(0px)" }}
              transition={{ duration: 1, delay: 2.5 }}
              className="text-lg md:text-xl text-zinc-400 mb-8 max-w-2xl mx-auto"
            >
              Meet the new standard for modern software development. Streamline
              issues, sprints, and product roadmaps.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 3 }}
              className="flex flex-col sm:flex-row gap-4 justify-center"
            >
              <Button
                size="lg"
                className="bg-white text-black hover:bg-zinc-200 transition-colors"
              >
                Get Started
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-zinc-700 text-white hover:bg-zinc-800 transition-colors"
              >
                View Demo
              </Button>
            </motion.div>
          </motion.div>

          {/* 3D Card Animation */}
          <motion.div
            className="mt-16 relative"
            initial={{ opacity: 0, y: 100 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 3.5 }}
          >
            <div className="relative w-full aspect-[16/9] bg-gradient-to-br from-zinc-800 to-zinc-900 rounded-xl overflow-hidden border border-zinc-800">
              <motion.div
                className="absolute inset-0 bg-gradient-to-br from-violet-500/10 to-fuchsia-500/10"
                animate={{
                  opacity: [0.5, 0.3, 0.5],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "linear",
                }}
              />
              {children}
            </div>

            {/* Reflection Effect */}
            <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent" />
          </motion.div>
        </div>
      </div>
    );
  }
  ```

  <Step>Install required dependencies</Step>
  ```bash
  pnpm add framer-motion
  ```

  <Step>Import and use the component</Step>
  ```tsx
  import Hero from "@/components/sections/hero"

  export default function Page() {
    return (
      <Hero>
        {/* Optional child content for the 3D card section */}
      </Hero>
    )
  }
  ```
</Steps>

## Customization

### Changing the Text Content

To modify the hero text and description, update the `text` prop in the `WordReveal` component and the content in the motion paragraph:

```tsx
<WordReveal
  text="Your Custom Headline Here"
  className="mb-6"
  delay={0.2}
/>

<motion.p>
  Your custom description text here.
</motion.p>
```

### Modifying the Gradient Background

The gradient background can be customized by adjusting the color values in the className:

```tsx
<motion.div
  className="absolute w-[1000px] h-[1000px] rounded-full bg-gradient-to-r from-[your-color]/30 to-[your-color]/30 blur-3xl"
  // ...
/>
```

### Adjusting Animations

The animation timing and effects can be modified through the Framer Motion properties:

```tsx
<motion.div
  animate={{
    scale: [1, 1.2, 1], // Increase scale range
    opacity: [0.4, 0.2, 0.4], // Adjust opacity
    x: [0, 150, 0], // Modify movement range
    y: [0, 75, 0],
  }}
  transition={{
    duration: 10, // Slower animation
    repeat: Infinity,
    ease: "easeInOut", // Different easing function
  }}
/>
```

## Props

| Prop      | Type      | Description                                     | Default |
| --------- | --------- | ----------------------------------------------- | ------- |
| className | string    | Additional classes to apply to the container    | -       |
| children  | ReactNode | Optional content for the 3D card section        | -       |

## Examples

### Basic Hero

```tsx
<Hero className="bg-gradient-to-b from-black to-zinc-900">
  {/* Optional content */}
</Hero>
```

### With Custom Content

```tsx
<Hero>
  <div className="p-6 text-white">
    <h3 className="text-xl font-bold">Custom Card Content</h3>
    <p>Add any content inside the 3D card section</p>
  </div>
</Hero>
```

## Accessibility

The hero section is built with accessibility in mind:

- Semantic HTML structure
- Proper heading hierarchy
- Sufficient color contrast
- Keyboard-navigable buttons
- Motion animations respect `prefers-reduced-motion`

<Note variant="warning">
  When customizing the component, ensure to maintain WCAG color contrast ratios and keep text content readable.
</Note> 