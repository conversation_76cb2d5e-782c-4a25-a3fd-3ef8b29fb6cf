---
title: PrismUI Homepage Template
publishedAt: 2025-01-02
summary: A modern, beautifully designed homepage template for your next project, built with Next.js 15 and PrismUI components.
author: codehagen
slug: templates/prismui-homepage
related: ["introduction"]
---

<div className="flex max-w-[800px] flex-col gap-4 mb-8">
  <video
    autoPlay
    loop
    muted
    playsInline
    src="/video/prismui-homepage.mp4"
    className="w-full rounded-xl border shadow-2xl"
  />
  <div className="flex w-full flex-col gap-2 sm:flex-row">
    <DownloadButton fileName="prismui-homepage.zip" />
    <Link 
      href="https://templete-prismui.vercel.app/" 
      className="flex-1 inline-flex h-9 items-center justify-center rounded-md border border-input bg-background px-4 py-2 text-sm font-medium shadow-sm hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
    >
      Live Preview
    </Link>
  </div>
</div>

<Note>
  A beautifully designed homepage template featuring modern animations, dark mode, and PrismUI components. Perfect for showcasing your SaaS, product, or service.
</Note>

## Features

<div className="grid sm:grid-cols-2 gap-4 mb-8">
  <div className="flex flex-col gap-2 p-4 rounded-lg border bg-card text-card-foreground shadow-sm">
    <div className="flex items-center gap-2">
      <span className="text-lg">✨</span>
      <h3 className="font-medium">Modern Tech Stack</h3>
    </div>
    <p className="text-sm text-muted-foreground">Built with Next.js 15, React Server Components, and PrismUI components for optimal performance.</p>
  </div>
  <div className="flex flex-col gap-2 p-4 rounded-lg border bg-card text-card-foreground shadow-sm">
    <div className="flex items-center gap-2">
      <span className="text-lg">🎨</span>
      <h3 className="font-medium">Beautiful Design</h3>
    </div>
    <p className="text-sm text-muted-foreground">Clean and minimal design with dark mode and smooth animations, powered by PrismUI.</p>
  </div>
  <div className="flex flex-col gap-2 p-4 rounded-lg border bg-card text-card-foreground shadow-sm">
    <div className="flex items-center gap-2">
      <span className="text-lg">📱</span>
      <h3 className="font-medium">Responsive</h3>
    </div>
    <p className="text-sm text-muted-foreground">Fully responsive design that looks great on all devices, from mobile to desktop.</p>
  </div>
  <div className="flex flex-col gap-2 p-4 rounded-lg border bg-card text-card-foreground shadow-sm">
    <div className="flex items-center gap-2">
      <span className="text-lg">⚡️</span>
      <h3 className="font-medium">Fast Setup</h3>
    </div>
    <p className="text-sm text-muted-foreground">Get your homepage up and running in minutes with our detailed documentation.</p>
  </div>
</div>

## Tech Stack

<div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-8">
  <div className="flex items-center gap-3 p-4 rounded-lg border bg-card text-card-foreground">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 2L2 7l10 5 10-5-10-5z"/><path d="M2 17l10 5 10-5"/><path d="M2 12l10 5 10-5"/></svg>
    <div>
      <div className="font-medium">Next.js 15</div>
      <div className="text-sm text-muted-foreground">React framework</div>
    </div>
  </div>
  <div className="flex items-center gap-3 p-4 rounded-lg border bg-card text-card-foreground">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="12" cy="12" r="10"/><circle cx="12" cy="12" r="4"/></svg>
    <div>
      <div className="font-medium">React</div>
      <div className="text-sm text-muted-foreground">UI library</div>
    </div>
  </div>
  <div className="flex items-center gap-3 p-4 rounded-lg border bg-card text-card-foreground">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M20.24 12.24a6 6 0 0 0-8.49-8.49L5 10.5V19h8.5z"/><line x1="16" y1="8" x2="2" y2="22"/><line x1="17.5" y1="15" x2="9" y2="15"/></svg>
    <div>
      <div className="font-medium">Tailwind CSS</div>
      <div className="text-sm text-muted-foreground">Styling</div>
    </div>
  </div>
  <div className="flex items-center gap-3 p-4 rounded-lg border bg-card text-card-foreground">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"/></svg>
    <div>
      <div className="font-medium">TypeScript</div>
      <div className="text-sm text-muted-foreground">Type safety</div>
    </div>
  </div>
  <div className="flex items-center gap-3 p-4 rounded-lg border bg-card text-card-foreground">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/></svg>
    <div>
      <div className="font-medium">MDX</div>
      <div className="text-sm text-muted-foreground">Content</div>
    </div>
  </div>
  <div className="flex items-center gap-3 p-4 rounded-lg border bg-card text-card-foreground">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M5 8h14"/><path d="M5 12h14"/><path d="M5 16h14"/></svg>
    <div>
      <div className="font-medium">Framer Motion</div>
      <div className="text-sm text-muted-foreground">Animations</div>
    </div>
  </div>
</div>

## Getting Started

<Steps>
  <Step>Install dependencies</Step>
  ```bash
  pnpm install
  ```

  <Step>Set up your environment variables</Step>
  ```bash
  cp .env.example .env.local
  ```
  Then update the variables in `.env.local` with your own values.

  <Step>Start the development server</Step>
  ```bash
  pnpm dev
  ```

  <Step>View your application</Step>
  Open [http://localhost:3000](http://localhost:3000) in your browser to see your homepage.
</Steps>

