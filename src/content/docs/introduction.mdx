---
title: Welcome to PrismUI
publishedAt: 2025-01-02
summary: Get started building beautiful, animated, and accessible components with PrismUI.
author: codehagen
slug: introduction
---

# Introduction

Welcome to PrismUI, a collection of beautifully designed and animated components that you can copy and paste into your apps. Built on top of [shadcn/ui](https://ui.shadcn.com), [Framer Motion](https://framer.com/motion) and [Tailwind CSS](https://tailwindcss.com), PrismUI extends the foundation laid by these amazing tools to bring you more advanced, animated components.

<Quote
  author="<PERSON><PERSON>"
  authorSrc="https://imagedelivery.net/r-6-yk-gGPtjfbIST9-8uA/addc4b60-4c8f-47d7-10ab-6f9048432500/public"
  title="Creator of PrismUI"
  company="PrismUI"
  companySrc="/_static/logo.svg"
  text="The moment I discovered shadcn/ui, I knew it was revolutionary. I saw the community's growing desire for more animated and advanced components, and that's why I created PrismUI - to help bridge that gap while maintaining the same philosophy of ownership and customization."
/>

## Inspiration

PrismUI draws inspiration from two main sources:

1. **[shadcn/ui](https://x.com/shadcn)** - The foundation and philosophy behind PrismUI. Like shadcn/ui, this is not a component library but a collection of components you can copy, paste, and make your own. We build upon their excellent base components to create more advanced variations.

2. **[Steven Tey](https://x.com/steventey)** - The clean, modern UI/UX seen in projects like [dub.sh](https://dub.sh) has been a huge inspiration. Many of our components draw from his approach to creating delightful user experiences through subtle animations and thoughtful design.

## Why PrismUI?

- **Advanced Animations** - Built-in animations using Framer Motion for smooth, delightful interactions
- **Extended Components** - Takes shadcn/ui components further with additional features and variations
- **Modern UX Patterns** - Implements patterns seen in modern web apps like dub.sh and Vercel
- **Copy and Paste** - Use components directly in your app and customize them to your needs
- **Accessible** - All components follow WAI-ARIA guidelines
- **Dark Mode** - Built-in dark mode support with consistent animations
- **TypeScript** - Written in TypeScript for type safety
- **React Server Components** - Built for the Next.js 

## Philosophy

PrismUI follows the same philosophy as shadcn/ui: giving you ownership and control over the code. We believe that:

1. You should be able to read, modify, and maintain your components
2. Animations should enhance, not hinder, the user experience
3. Advanced features shouldn't come at the cost of accessibility
4. Components should be easy to customize and extend

## Credits

PrismUI wouldn't be possible without these amazing projects and people:

- [shadcn](https://twitter.com/shadcn) - For creating shadcn/ui and inspiring this project
- [Steven Tey](https://twitter.com/steventey) - For UI/UX inspiration and modern component patterns
- [Tailwind CSS](https://tailwindcss.com) - For the styling system
- [Framer Motion](https://framer.com/motion) - For the animation capabilities
- [Lucide](https://lucide.dev) - For the beautiful icons

## Community

Join our community to get help, share your creations, and contribute:

- [GitHub](https://github.com/codehagen/prismui)
- [Twitter](https://twitter.com/codehagen)
- [Discord](https://discord.gg/SAy7T2zy) 