---
title: Installation
publishedAt: 2025-01-02
summary: Learn how to install and set up PrismUI in your project.
author: codehagen
slug: installation
---

# Installation

PrismUI is built on top of [shadcn/ui](https://ui.shadcn.com), extending it with beautiful animations and advanced components. Follow these steps to set up your project.

## Setup

<Steps>
  <Step>Create a new Next.js project with shadcn/ui</Step>
  First, create a new Next.js project and initialize shadcn/ui:

  ```bash
  pnpm dlx shadcn@latest init
  ```

  You can use the `-d` flag for default settings (New York style, Zinc color, and CSS variables):

  ```bash
  pnpm dlx shadcn@latest init -d
  ```

  <Step>Configure components.json</Step>
  When running the init command, you'll be asked to configure your `components.json`:

  ```bash
  Which style would you like to use? › New York
  Which color would you like to use as base color? › Zinc
  Do you want to use CSS variables for colors? › yes
  ```

  <Step>Install additional dependencies</Step>
  PrismUI requires some additional dependencies for animations and advanced features:

  ```bash
  pnpm add framer-motion
  pnpm add tailwindcss-animate
  pnpm add lucide-react
  ```

  <Step>Install base shadcn/ui components</Step>
  PrismUI builds on top of several shadcn/ui base components. Install them:

  ```bash
  pnpm dlx shadcn@latest add button
  pnpm dlx shadcn@latest add card
  pnpm dlx shadcn@latest add dialog
  pnpm dlx shadcn@latest add separator
  ```

  <Step>Install PrismUI components</Step>
  Finally, install PrismUI components:

  ```bash
  pnpm dlx shadcn@latest add "https://www.prismui.tech/r/styles/default/expandable-card.json"
  ```

</Steps>

## Next Steps

Now that you have PrismUI set up, you can start using our animated components:

1. Browse the [components](/docs/components/expandable-card) section to see our extended component collection
2. Check out our [templates](/docs/components/hero) for example layouts
