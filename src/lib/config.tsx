export const BLUR_FADE_DELAY = 0.15;

export const siteConfig = {
  name: "NACE Certification",
  description: "Quality Assurance & Certification Excellence",
  url: process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000",
  keywords: ["ISO Certification", "Quality Management", "Certification Body", "ISO 9001", "ISO 27001"],
  links: {
    email: "<EMAIL>",
    phone: "+91-120-3170247",
    whatsapp: "+91-8285008681",
    address: "6C, Block-2, Sector-135, Noida-201304",
    facebook: "https://facebook.com/nacecertification",
    instagram: "https://instagram.com/nacecertification/",
  },
  header: [
    {
      href: "/about",
      label: "About Us",
    },
    {
      href: "/services",
      label: "Services",
    },
    {
      href: "/certification-process",
      label: "Certification Process",
    },
    {
      href: "/contact",
      label: "Contact",
    },
  ],
  pricing: [
    {
      name: "BASIC",
      href: "#",
      price: "$19",
      period: "month",
      yearlyPrice: "$16",
      features: [
        "1 User",
        "5GB Storage",
        "Basic Support",
        "Limited API Access",
        "Standard Analytics",
      ],
      description: "Perfect for individuals and small projects",
      buttonText: "Subscribe",
      isPopular: false,
    },
    {
      name: "PRO",
      href: "#",
      price: "$49",
      period: "month",
      yearlyPrice: "$40",
      features: [
        "5 Users",
        "50GB Storage",
        "Priority Support",
        "Full API Access",
        "Advanced Analytics",
      ],
      description: "Ideal for growing businesses and teams",
      buttonText: "Subscribe",
      isPopular: true,
    },
    {
      name: "ENTERPRISE",
      href: "#",
      price: "$99",
      period: "month",
      yearlyPrice: "$82",
      features: [
        "Unlimited Users",
        "500GB Storage",
        "24/7 Premium Support",
        "Custom Integrations",
        "AI-Powered Insights",
      ],
      description: "For large-scale operations and high-volume users",
      buttonText: "Subscribe",
      isPopular: false,
    },
  ],
};

export type SiteConfig = typeof siteConfig;
